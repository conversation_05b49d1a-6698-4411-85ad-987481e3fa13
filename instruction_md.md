# Winova Agency Website - Development Instructions

## Project Overview
**Winova** is a Luxembourg-based creative agency specializing in:
- Content creation
- Photography (real estate, restaurants, local shops)
- Web development

## Tech Stack & Architecture

### Core Technologies
- **Next.js 14** (App Router, Server Actions) with TypeScript
- **Tailwind CSS** with custom Winova theme
- **Framer Motion** for scroll-triggered animations and parallax
- **Shadcn/UI** for modern UI components
- **React Server Components (RSC)** for optimized rendering

### Performance & SEO
- **next-seo** for structured meta tags
- **next-sitemap** for automatic sitemap generation
- **Image Optimization** with next/image (AVIF/WebP)
- **Font Optimization** with Google Fonts Inter
- **ISR/SSG** for SEO-friendly pages

### Animation & Interactions
- **Framer Motion** keyframes and scroll-linked animations
- **Parallax effects** using useScroll + useTransform
- **Magnetic buttons** and floating cards
- **Modern micro-interactions** inspired by Revolut

## Brand Identity

### Color Palette
- **Primary**: #122842 (Deep Navy)
- **Secondary**: #262626 (Charcoal)
- **Accent**: #FAFCFC (Off-white)
- **Luxury Gradient**: Linear gradient from #122842 → #1e3a8a → #3b82f6

### Typography
- **Primary Font**: Inter (Google Fonts)
- **Mono Font**: JetBrains Mono / Fira Code
- **Font Features**: cv11, ss01, variable font settings

## Development Milestones

### ✅ Step 1: Project Initialization (COMPLETED)
- [x] Next.js 14 project setup with TypeScript
- [x] Tailwind CSS configuration
- [x] Shadcn/UI initialization
- [x] Essential dependencies installation:
  - framer-motion
  - @radix-ui/react-slot
  - class-variance-authority
  - clsx, tailwind-merge
  - lucide-react
  - next-seo, next-sitemap
  - react-hook-form, @hookform/resolvers, zod
  - react-wrap-balancer
  - tailwindcss-animate

### ✅ Step 2: Design System Setup (COMPLETED)
- [x] Custom Tailwind config with Winova brand colors
- [x] CSS variables for light/dark themes
- [x] Custom animations and keyframes
- [x] Utility classes for:
  - Text reveal animations
  - Gradient text effects
  - Glass morphism
  - Magnetic hover effects
  - Smooth reveal animations
  - Parallax containers
- [x] Custom scrollbar styling
- [x] Inter font integration

### 🔄 Step 3: SEO Configuration (IN PROGRESS)
- [ ] next-seo setup with default SEO config
- [ ] JSON-LD schema for local business
- [ ] next-sitemap configuration
- [ ] robots.txt setup
- [ ] Open Graph and Twitter meta tags

### 📋 Step 4: Core Components
- [ ] Navigation component with smooth animations
- [ ] Footer component
- [ ] Button components (magnetic, gradient variants)
- [ ] Card components with hover effects
- [ ] Modal/Dialog components
- [ ] Form components with validation

### 📋 Step 5: Home Page Implementation
- [ ] Hero section with parallax video/photo background
- [ ] Smooth scroll and text reveal animations
- [ ] Services preview section
- [ ] Portfolio showcase
- [ ] Call-to-action sections

### 📋 Step 6: Additional Pages
- [ ] Services page with detailed offerings
- [ ] Portfolio page with interactive grid
- [ ] About Us page with timeline animation
- [ ] Contact page with optimized form

### 📋 Step 7: Advanced Animations
- [ ] Scroll-linked parallax effects
- [ ] GSAP integration for complex timelines
- [ ] Magnetic button interactions
- [ ] Floating card animations
- [ ] Page transition animations

### 📋 Step 8: Performance Optimization
- [ ] Image optimization and lazy loading
- [ ] Code splitting and dynamic imports
- [ ] Core Web Vitals optimization
- [ ] Lighthouse score optimization (target: 95+)
- [ ] Bundle analysis and tree shaking

### 📋 Step 9: SEO & Analytics
- [ ] Structured data implementation
- [ ] Meta tags optimization
- [ ] Analytics integration (Vercel Analytics)
- [ ] Performance monitoring setup

### 📋 Step 10: Production Deployment
- [ ] Vercel deployment configuration
- [ ] Environment variables setup
- [ ] Domain configuration
- [ ] SSL and security headers
- [ ] Final testing and QA

## File Structure
```
winova/
├── src/
│   ├── app/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   └── favicon.ico
│   ├── components/
│   │   ├── ui/           # Shadcn/UI components
│   │   ├── layout/       # Navigation, Footer
│   │   ├── sections/     # Page sections
│   │   └── animations/   # Animation components
│   ├── lib/
│   │   ├── utils.ts
│   │   ├── seo.ts
│   │   └── animations.ts
│   └── styles/
├── public/
│   ├── images/
│   ├── videos/
│   └── icons/
├── tailwind.config.ts
├── next.config.ts
├── components.json
└── package.json
```

## Development Notes

### Current Status
- Project successfully initialized with Next.js 14 and TypeScript
- Tailwind CSS configured with Winova brand colors and custom design system
- Shadcn/UI components library ready for use
- Custom CSS utilities and animations implemented
- Inter font integrated for modern typography

### Next Steps
1. Configure SEO with next-seo and structured data
2. Create core UI components (Navigation, Footer, Buttons)
3. Implement Hero section with parallax animations
4. Build out remaining pages with scroll-triggered animations

### Performance Targets
- **Lighthouse Performance**: ≥95
- **First Contentful Paint**: <1.5s
- **Largest Contentful Paint**: <2.5s
- **Cumulative Layout Shift**: <0.1
- **First Input Delay**: <100ms

### Design Inspiration
- **Revolut**: Premium, minimalistic design with smooth animations
- **Modern luxury**: Clean UI with sophisticated micro-interactions
- **Visual storytelling**: Strong emphasis on photography and video content

---

*Last updated: Project initialization and design system setup completed*