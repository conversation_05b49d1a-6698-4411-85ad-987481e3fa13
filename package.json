{"name": "<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.0", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-slot": "^1.2.3", "@react-spring/web": "^10.0.1", "@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.2.0", "@studio-freight/lenis": "^1.0.42", "@types/three": "^0.178.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.9", "gsap": "^3.13.0", "lenis": "^1.3.8", "lucide-react": "^0.525.0", "next": "15.4.4", "next-seo": "^6.8.0", "next-sitemap": "^4.2.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.61.1", "react-intersection-observer": "^9.16.0", "react-tsparticles": "^2.12.2", "react-wrap-balancer": "^1.1.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "tsparticles": "^3.8.1", "zod": "^4.0.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}