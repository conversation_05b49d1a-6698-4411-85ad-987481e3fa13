'use client'

import React, { useRef, useEffect } from 'react'
import { motion, useInView } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import Header from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import HeroPhoto from '@/components/sections/HeroPhoto'
import CleanContact from '@/components/CleanContact'

gsap.registerPlugin(ScrollTrigger)

export default function About() {
  const timelineRef = useRef<HTMLDivElement>(null)
  const statsRef = useRef<HTMLDivElement>(null)
  const isTimelineInView = useInView(timelineRef, { once: true, margin: "-100px" })
  const isStatsInView = useInView(statsRef, { once: true, margin: "-100px" })

  useEffect(() => {
    if (isStatsInView && statsRef.current) {
      const counters = statsRef.current.querySelectorAll('[data-count]')
      
      counters.forEach((counter) => {
        const target = parseInt(counter.getAttribute('data-count') || '0')
        gsap.fromTo(counter, 
          { textContent: 0 },
          {
            textContent: target,
            duration: 2,
            ease: "power2.out",
            snap: { textContent: 1 },
            onUpdate: function() {
              counter.textContent = Math.ceil(this.targets()[0].textContent)
            }
          }
        )
      })
    }
  }, [isStatsInView])

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      <Header />
      
      {/* About Hero */}
      <HeroPhoto 
        title="About Winova"
        subtitle="Creative minds crafting digital excellence in Luxembourg"
        ctaText="Our Story"
        ctaLink="#story"
        height="large"
        photoQuery="creative team collaboration luxembourg"
        overlay="gradient"
      />
      
      {/* Our Story Section */}
      <section id="story" className="py-24 md:py-32 bg-white relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-64 h-64 bg-gray-50 rounded-full opacity-40" />
          <div className="absolute bottom-20 right-20 w-32 h-32 bg-gray-900 opacity-5 rotate-45" />
        </div>

        <div className="relative z-10 max-w-6xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
                Our Story
              </p>
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-8">
                Born in Luxembourg
              </h2>
              <div className="space-y-6 text-lg text-gray-600 leading-relaxed">
                <p>
                  Winova was founded with a simple yet powerful vision: to help businesses in Luxembourg and beyond 
                  tell their stories through exceptional creative work and cutting-edge digital solutions.
                </p>
                <p>
                  Based in the heart of Europe, we combine local expertise with global perspectives, creating 
                  content that resonates across cultures and markets. Our team brings together diverse talents 
                  in photography, videography, and web development.
                </p>
                <p>
                  From capturing the essence of Luxembourg's vibrant business landscape to developing digital 
                  experiences that drive growth, we're passionate about helping our clients succeed in an 
                  increasingly connected world.
                </p>
              </div>
            </div>
            
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=600&h=400&fit=crop&crop=entropy&auto=format&q=85"
                alt="Team collaboration"
                className="w-full h-96 object-cover rounded-2xl shadow-2xl"
              />
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-primary-950 rounded-2xl opacity-20"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section ref={statsRef} className="py-24 md:py-32 bg-gray-50 relative overflow-hidden">
        <div className="relative z-10 max-w-6xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              By the Numbers
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Our commitment to excellence is reflected in our growing portfolio and satisfied clients.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { number: 150, label: "Projects Completed", suffix: "+" },
              { number: 50, label: "Happy Clients", suffix: "+" },
              { number: 3, label: "Years Experience", suffix: "" },
              { number: 24, label: "Hour Support", suffix: "/7" }
            ].map((stat, index) => (
              <motion.div 
                key={index}
                className="text-center"
                initial={{ opacity: 0, y: 50 }}
                animate={isStatsInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
              >
                <div className="text-4xl md:text-5xl lg:text-6xl font-bold text-primary-950 mb-2">
                  <span data-count={stat.number}>0</span>{stat.suffix}
                </div>
                <p className="text-gray-600 font-medium">{stat.label}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-24 md:py-32 bg-white relative overflow-hidden">
        <div className="relative z-10 max-w-6xl mx-auto px-6">
          <div className="text-center mb-20">
            <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
              Our Values
            </p>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              What Drives Us
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            {[
              {
                title: "Excellence",
                description: "We never compromise on quality. Every project receives our full attention and expertise, ensuring results that exceed expectations.",
                icon: "⭐"
              },
              {
                title: "Innovation", 
                description: "We stay ahead of trends and technologies, bringing fresh perspectives and cutting-edge solutions to every challenge.",
                icon: "💡"
              },
              {
                title: "Partnership",
                description: "We believe in building lasting relationships with our clients, working together as partners to achieve shared success.",
                icon: "🤝"
              }
            ].map((value, index) => (
              <motion.div 
                key={index}
                className="text-center group"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true, margin: "-100px" }}
              >
                <div className="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  {value.icon}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {value.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section ref={timelineRef} className="py-24 md:py-32 bg-gray-50 relative overflow-hidden">
        <div className="relative z-10 max-w-4xl mx-auto px-6">
          <div className="text-center mb-20">
            <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
              Our Journey
            </p>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Milestones
            </h2>
          </div>

          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-8 top-0 bottom-0 w-px bg-gray-300"></div>
            
            <div className="space-y-12">
              {[
                {
                  year: "2022",
                  title: "Foundation",
                  description: "Winova was founded with a vision to bring exceptional creative services to Luxembourg's growing business community."
                },
                {
                  year: "2023", 
                  title: "Growth",
                  description: "Expanded our services to include comprehensive web development and established partnerships with key local businesses."
                },
                {
                  year: "2024",
                  title: "Innovation",
                  description: "Launched our advanced digital solutions portfolio and began serving international clients across Europe."
                },
                {
                  year: "2025",
                  title: "Future",
                  description: "Continuing to push boundaries in creative technology and expanding our impact in the digital landscape."
                }
              ].map((milestone, index) => (
                <motion.div 
                  key={index}
                  className="relative flex items-start"
                  initial={{ opacity: 0, x: -50 }}
                  animate={isTimelineInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
                  transition={{ duration: 0.8, delay: index * 0.3 }}
                >
                  <div className="flex-shrink-0 w-16 h-16 bg-primary-950 rounded-full flex items-center justify-center text-white font-bold text-lg relative z-10">
                    {milestone.year.slice(-2)}
                  </div>
                  <div className="ml-8 pb-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {milestone.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {milestone.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <CleanContact />
      
      <Footer />
    </div>
  )
}
