@import "tailwindcss";
@import "tw-animate-css";

/* 3D Carousel Styles */
.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  /* Winova Brand Variables */
  --font-inter: 'Inter', system-ui, -apple-system, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* Design System */
  --radius: 0.75rem;

  /* Winova Light Theme */
  --background: #FAFCFC;
  --foreground: #122842;
  --card: #FFFFFF;
  --card-foreground: #122842;
  --popover: #FFFFFF;
  --popover-foreground: #122842;
  --primary: #122842;
  --primary-foreground: #FAFCFC;
  --secondary: #262626;
  --secondary-foreground: #FAFCFC;
  --muted: #F8FAFC;
  --muted-foreground: #64748B;
  --accent: #FAFCFC;
  --accent-foreground: #122842;
  --destructive: #EF4444;
  --destructive-foreground: #FAFCFC;
  --border: #E2E8F0;
  --input: #F1F5F9;
  --ring: #122842;

  /* Chart Colors */
  --chart-1: #122842;
  --chart-2: #262626;
  --chart-3: #3B82F6;
  --chart-4: #10B981;
  --chart-5: #F59E0B;

  /* Sidebar */
  --sidebar: #FFFFFF;
  --sidebar-foreground: #122842;
  --sidebar-primary: #122842;
  --sidebar-primary-foreground: #FAFCFC;
  --sidebar-accent: #F8FAFC;
  --sidebar-accent-foreground: #122842;
  --sidebar-border: #E2E8F0;
  --sidebar-ring: #122842;
}

.dark {
  /* Winova Dark Theme */
  --background: #0A0A0A;
  --foreground: #FAFCFC;
  --card: #1A1A1A;
  --card-foreground: #FAFCFC;
  --popover: #1A1A1A;
  --popover-foreground: #FAFCFC;
  --primary: #FAFCFC;
  --primary-foreground: #122842;
  --secondary: #262626;
  --secondary-foreground: #FAFCFC;
  --muted: #1A1A1A;
  --muted-foreground: #94A3B8;
  --accent: #1A1A1A;
  --accent-foreground: #FAFCFC;
  --destructive: #EF4444;
  --destructive-foreground: #FAFCFC;
  --border: #2A2A2A;
  --input: #1A1A1A;
  --ring: #FAFCFC;

  /* Chart Colors Dark */
  --chart-1: #3B82F6;
  --chart-2: #10B981;
  --chart-3: #F59E0B;
  --chart-4: #EF4444;
  --chart-5: #8B5CF6;

  /* Sidebar Dark */
  --sidebar: #1A1A1A;
  --sidebar-foreground: #FAFCFC;
  --sidebar-primary: #3B82F6;
  --sidebar-primary-foreground: #FAFCFC;
  --sidebar-accent: #262626;
  --sidebar-accent-foreground: #FAFCFC;
  --sidebar-border: #2A2A2A;
  --sidebar-ring: #FAFCFC;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  html {
    /* Lenis will handle smooth scrolling */
    overscroll-behavior: none;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
    cursor: url("data:image/svg+xml,%3csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='10' cy='10' r='8' fill='none' stroke='%23000000' stroke-width='2'/%3e%3ccircle cx='10' cy='10' r='2' fill='%23000000'/%3e%3c/svg%3e") 10 10, auto;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Smooth cursor transitions for all elements */
  * {
    transition: cursor 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Interactive cursor states */
  a, button, [role="button"], .cursor-pointer {
    cursor: url("data:image/svg+xml,%3csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='12' cy='12' r='10' fill='none' stroke='%232563eb' stroke-width='2'/%3e%3ccircle cx='12' cy='12' r='3' fill='%232563eb'/%3e%3c/svg%3e") 12 12, pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  a:hover, button:hover, [role="button"]:hover, .cursor-pointer:hover {
    cursor: url("data:image/svg+xml,%3csvg width='28' height='28' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='14' cy='14' r='12' fill='none' stroke='%232563eb' stroke-width='2'/%3e%3ccircle cx='14' cy='14' r='4' fill='%232563eb'/%3e%3c/svg%3e") 14 14, pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  a:active, button:active, [role="button"]:active, .cursor-pointer:active {
    cursor: url("data:image/svg+xml,%3csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='10' cy='10' r='8' fill='none' stroke='%231d4ed8' stroke-width='3'/%3e%3ccircle cx='10' cy='10' r='2' fill='%231d4ed8'/%3e%3c/svg%3e") 10 10, pointer;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Premium Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted/50;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border/60 rounded-full;
    transition: all 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-ring/80;
    width: 10px;
  }

  /* Smooth scroll enhancements */
  * {
    scroll-margin-top: 2rem;
  }

  /* Prevent scroll bounce on mobile */
  body {
    overscroll-behavior-y: none;
    -webkit-overflow-scrolling: touch;
  }
}

@layer utilities {
  /* Text reveal animation */
  .text-reveal {
    background: linear-gradient(to right, transparent 50%, currentColor 50%);
    background-size: 200% 100%;
    background-position: 100% 0;
    transition: background-position 0.3s ease;
  }

  .text-reveal.active {
    background-position: 0 0;
  }

  /* Luxury gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary via-blue-600 to-blue-400 bg-clip-text text-transparent;
  }

  /* Glass morphism */
  .glass {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }

  /* Photo animation utilities */
  .photo-hover {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .photo-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  /* Image loading animation */
  .image-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  /* Parallax container */
  .parallax-container {
    transform-style: preserve-3d;
    perspective: 1000px;
  }

  /* Photo grid masonry */
  .masonry-grid {
    column-fill: balance;
    column-gap: 1.5rem;
  }

  .masonry-item {
    break-inside: avoid;
    margin-bottom: 1.5rem;
  }

  /* Lightbox animations */
  .lightbox-enter {
    opacity: 0;
    transform: scale(0.8);
  }

  .lightbox-enter-active {
    opacity: 1;
    transform: scale(1);
    transition: opacity 300ms, transform 300ms;
  }

  .lightbox-exit {
    opacity: 1;
    transform: scale(1);
  }

  .lightbox-exit-active {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 300ms, transform 300ms;
  }

  /* Image reveal animation */
  .image-reveal {
    position: relative;
    overflow: hidden;
  }

  .image-reveal::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
    z-index: 1;
  }

  .image-reveal:hover::before {
    left: 100%;
  }

  /* Photo filter effects */
  .photo-filter-vintage {
    filter: sepia(0.3) contrast(1.2) brightness(1.1) saturate(0.8);
  }

  .photo-filter-bw {
    filter: grayscale(1) contrast(1.1) brightness(1.1);
  }

  .photo-filter-warm {
    filter: sepia(0.2) saturate(1.3) hue-rotate(10deg) brightness(1.05);
  }

  .photo-filter-cool {
    filter: saturate(1.2) hue-rotate(-10deg) brightness(1.05) contrast(1.1);
  }

  /* Responsive image utilities */
  .responsive-image {
    width: 100%;
    height: auto;
    object-fit: cover;
    transition: all 0.3s ease;
  }

  /* Line clamp utilities for photo descriptions */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Enhanced animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes scale-pulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-rotate {
    animation: rotate 20s linear infinite;
  }

  .animate-scale-pulse {
    animation: scale-pulse 4s ease-in-out infinite;
  }

  /* Perspective utilities */
  .perspective-1000 {
    perspective: 1000px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }

  /* Enhanced background patterns */
  .bg-pattern-dots {
    background-image: radial-gradient(circle, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .bg-pattern-grid {
    background-image:
      linear-gradient(rgba(0,0,0,0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0,0,0,0.05) 1px, transparent 1px);
    background-size: 30px 30px;
  }

  /* Gradient text utilities */
  .text-gradient-blue {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-purple {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .glass-dark {
    @apply backdrop-blur-md bg-black/10 border border-white/10;
  }

  /* Magnetic hover effect */
  .magnetic {
    transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* Smooth reveal animations */
  .reveal-up {
    transform: translateY(30px);
    opacity: 0;
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .reveal-up.active {
    transform: translateY(0);
    opacity: 1;
  }

  /* Parallax container */
  .parallax-container {
    overflow: hidden;
    will-change: transform;
  }
}
