'use client'

import React from 'react'
import Header from '@/components/layout/Header'
import CleanHero from '@/components/CleanHero'
import CleanServices from '@/components/CleanServices'
import CreativeWork from '@/components/CreativeWork'
import CleanPhotoGallery from '@/components/sections/CleanPhotoGallery';
import CleanContact from '@/components/CleanContact'
import { Footer } from '@/components/layout/Footer'

export default function Home() {
  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      <Header />

      <CleanHero
        title="We create digital experiences that matter"
        subtitle=""
        description="We're a creative agency helping brands tell their story through beautiful, functional digital experiences."
        ctaText="Start a project"
        ctaLink="/contact"
        secondaryCtaText="View our work"
        secondaryCtaLink="/portfolio"
      />

      <CleanServices />

      <CreativeWork
        title="Our Creative Work"
        subtitle="Photography • Video • Digital Experiences"
        maxItems={6}
      />

      <CleanPhotoGallery
        title="Our approach"
        subtitle="How we work"
        description="Every project is unique, but our process remains consistent. We believe in collaboration, iteration, and attention to detail."
        photoQuery="team collaboration workspace"
        category="business"
        maxPhotos={8}
        columns={4}
      />

      <CleanContact />

      <Footer />
    </div>
  );
}
