'use client'

import React from 'react'
import Header from '@/components/layout/Header'
import HeroPhoto from '@/components/sections/HeroPhoto'
import PhotoGallery from '@/components/sections/PhotoGallery'
import PhotoShowcase from '@/components/sections/PhotoShowcase'
import AdvancedContact from '@/components/sections/AdvancedContact'
import { Footer } from '@/components/layout/Footer'

export default function Portfolio() {
  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Header */}
      <Header />

      {/* Portfolio Hero */}
      <HeroPhoto
        title="Our Portfolio"
        subtitle="Explore our creative journey through stunning visuals and innovative design solutions"
        ctaText="View Gallery"
        ctaLink="#gallery"
        height="large"
        photoQuery="portfolio creative work design"
        overlay="gradient"
      />

      {/* Featured Projects Showcase */}
      <div id="featured">
        <PhotoShowcase
          title="Featured Projects"
          subtitle="Our most impactful work that defines our creative excellence"
          category="creative"
          maxItems={9}
          layout="masonry"
          showCategories={true}
        />
      </div>

      {/* Creative Gallery */}
      <div id="gallery">
        <PhotoGallery
          title="Creative Gallery"
          subtitle="A comprehensive collection showcasing our diverse creative capabilities"
          category="creative"
          maxPhotos={18}
          columns={4}
          showFilters={true}
        />
      </div>

      {/* Design Showcase */}
      <PhotoShowcase
        title="Design Excellence"
        subtitle="Innovative design solutions that push creative boundaries"
        category="creative"
        maxItems={6}
        layout="grid"
        showCategories={false}
      />

      {/* Photography Collection */}
      <PhotoGallery
        title="Photography Collection"
        subtitle="Capturing moments and creating visual narratives that inspire"
        category="photography"
        maxPhotos={15}
        columns={3}
        showFilters={true}
        className="bg-gray-900 text-white"
      />

      {/* Technology & Innovation */}
      <PhotoShowcase
        title="Technology & Innovation"
        subtitle="Where creativity meets cutting-edge technology"
        category="technology"
        maxItems={8}
        layout="carousel"
        showCategories={false}
      />

      {/* Contact Section */}
      <div id="contact">
        <AdvancedContact />
      </div>

      <Footer />
    </div>
  )
}