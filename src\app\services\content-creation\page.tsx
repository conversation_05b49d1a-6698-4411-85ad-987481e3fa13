'use client'

import React from 'react'
import Header from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import HeroPhoto from '@/components/sections/HeroPhoto'
import CleanContact from '@/components/CleanContact'

export default function ContentCreation() {
  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      <Header />
      
      {/* Content Creation Hero */}
      <HeroPhoto 
        title="Content Creation"
        subtitle="Strategic content that drives engagement and results"
        ctaText="Start Your Project"
        ctaLink="#contact"
        height="large"
        photoQuery="content creation video production"
        overlay="gradient"
      />
      
      {/* Service Overview */}
      <section className="py-24 md:py-32 bg-white relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-64 h-64 bg-gray-50 rounded-full opacity-40" />
          <div className="absolute bottom-20 right-20 w-32 h-32 bg-gray-900 opacity-5 rotate-45" />
        </div>

        <div className="relative z-10 max-w-6xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
                What We Offer
              </p>
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-8">
                Content That Converts
              </h2>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                From compelling copywriting to engaging video content, we create strategic content 
                that tells your brand story and drives meaningful engagement with your audience.
              </p>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gray-900 rounded-full mr-4"></div>
                  <span className="text-gray-700">Strategic content planning & development</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gray-900 rounded-full mr-4"></div>
                  <span className="text-gray-700">Professional video production & editing</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gray-900 rounded-full mr-4"></div>
                  <span className="text-gray-700">Social media content & campaigns</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gray-900 rounded-full mr-4"></div>
                  <span className="text-gray-700">Copywriting & brand messaging</span>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl overflow-hidden">
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-4 flex items-center justify-center">
                      <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <p className="text-gray-600 font-medium">Video Production</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Breakdown */}
      <section className="py-24 md:py-32 bg-gray-50 relative overflow-hidden">
        <div className="relative z-10 max-w-6xl mx-auto px-6">
          <div className="text-center mb-20">
            <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
              Our Services
            </p>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Content Creation Services
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Video Production */}
            <div className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow duration-300">
              <div className="w-12 h-12 bg-gray-900 rounded-xl flex items-center justify-center mb-6">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Video Production</h3>
              <p className="text-gray-600 mb-6">
                Professional video content from concept to final edit, including promotional videos, 
                social media content, and brand storytelling.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Concept development & scripting</li>
                <li>• Professional filming & direction</li>
                <li>• Post-production & editing</li>
                <li>• Motion graphics & animation</li>
              </ul>
            </div>

            {/* Copywriting */}
            <div className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow duration-300">
              <div className="w-12 h-12 bg-gray-900 rounded-xl flex items-center justify-center mb-6">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Copywriting</h3>
              <p className="text-gray-600 mb-6">
                Compelling copy that converts, from website content and blog posts to 
                marketing materials and brand messaging.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Website copy & landing pages</li>
                <li>• Blog posts & articles</li>
                <li>• Marketing materials</li>
                <li>• Brand messaging & tone</li>
              </ul>
            </div>

            {/* Social Media */}
            <div className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow duration-300">
              <div className="w-12 h-12 bg-gray-900 rounded-xl flex items-center justify-center mb-6">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v3M7 4H5a1 1 0 00-1 1v16a1 1 0 001 1h14a1 1 0 001-1V5a1 1 0 00-1-1h-2M7 4h10M9 9h6m-6 4h6m-6 4h6" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Social Media Content</h3>
              <p className="text-gray-600 mb-6">
                Engaging social media content and campaigns that build community and 
                drive engagement across all platforms.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Content strategy & planning</li>
                <li>• Visual content creation</li>
                <li>• Campaign development</li>
                <li>• Community management</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-24 md:py-32 bg-white relative overflow-hidden">
        <div className="relative z-10 max-w-6xl mx-auto px-6">
          <div className="text-center mb-20">
            <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
              Our Process
            </p>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              How We Create Content
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Strategy</h3>
              <p className="text-gray-600">
                We start by understanding your brand, audience, and goals to develop a comprehensive content strategy.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Creation</h3>
              <p className="text-gray-600">
                Our team creates high-quality content that aligns with your brand voice and resonates with your audience.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Review</h3>
              <p className="text-gray-600">
                We collaborate with you to refine and perfect the content, ensuring it meets your expectations.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white font-bold text-xl">4</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Delivery</h3>
              <p className="text-gray-600">
                Final content is delivered in your preferred format, ready for publication and distribution.
              </p>
            </div>
          </div>
        </div>
      </section>

      <CleanContact />
      <Footer />
    </div>
  );
}
