'use client'

import React from 'react'
import Header from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import HeroPhoto from '@/components/sections/HeroPhoto'
import CleanContact from '@/components/CleanContact'
import Photography3DCarousel from '@/components/Photography3DCarousel'

export default function Photography() {
  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      <Header />

      {/* Photography Hero */}
      <HeroPhoto
        title="Photography"
        subtitle="Professional photography that captures your brand essence"
        ctaText="Book a Session"
        ctaLink="#contact"
        height="large"
        photoQuery="professional photography studio brand"
        overlay="gradient"
      />

      {/* Service Overview */}
      <section className="py-24 md:py-32 bg-white relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-64 h-64 bg-gray-50 rounded-full opacity-40" />
          <div className="absolute bottom-20 right-20 w-32 h-32 bg-gray-900 opacity-5 rotate-45" />
        </div>

        <div className="relative z-10 max-w-6xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
                What We Capture
              </p>
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-8">
                Visual Stories That Inspire
              </h2>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                From product photography that showcases your offerings to corporate portraits
                that humanize your brand, we create compelling visual narratives that connect
                with your audience.
              </p>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gray-900 rounded-full mr-4"></div>
                  <span className="text-gray-700">Product & commercial photography</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gray-900 rounded-full mr-4"></div>
                  <span className="text-gray-700">Corporate headshots & team photos</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gray-900 rounded-full mr-4"></div>
                  <span className="text-gray-700">Brand lifestyle photography</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gray-900 rounded-full mr-4"></div>
                  <span className="text-gray-700">Event & corporate documentation</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl overflow-hidden">
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-4 flex items-center justify-center">
                      <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <p className="text-gray-600 font-medium">Professional Photography</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Photography Services */}
      <section className="py-24 md:py-32 bg-gray-50 relative overflow-hidden">
        <div className="relative z-10 max-w-6xl mx-auto px-6">
          <div className="text-center mb-20">
            <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
              Our Services
            </p>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Photography Services
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Product Photography */}
            <div className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow duration-300">
              <div className="w-12 h-12 bg-gray-900 rounded-xl flex items-center justify-center mb-6">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Product Photography</h3>
              <p className="text-gray-600 mb-6">
                High-quality product images that showcase your offerings in the best light,
                perfect for e-commerce and marketing materials.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Studio product photography</li>
                <li>• Lifestyle product shots</li>
                <li>• 360° product views</li>
                <li>• E-commerce optimization</li>
              </ul>
            </div>

            {/* Corporate Photography */}
            <div className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow duration-300">
              <div className="w-12 h-12 bg-gray-900 rounded-xl flex items-center justify-center mb-6">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Corporate Photography</h3>
              <p className="text-gray-600 mb-6">
                Professional headshots and team photography that puts a human face
                on your business and builds trust with clients.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Executive headshots</li>
                <li>• Team photography</li>
                <li>• Office environment shots</li>
                <li>• LinkedIn profile photos</li>
              </ul>
            </div>

            {/* Brand Photography */}
            <div className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow duration-300">
              <div className="w-12 h-12 bg-gray-900 rounded-xl flex items-center justify-center mb-6">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Brand Photography</h3>
              <p className="text-gray-600 mb-6">
                Lifestyle and brand photography that tells your story and creates
                an emotional connection with your audience.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Brand lifestyle photography</li>
                <li>• Behind-the-scenes content</li>
                <li>• Social media imagery</li>
                <li>• Marketing campaign visuals</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-24 md:py-32 bg-white relative overflow-hidden">
        <div className="relative z-10 max-w-6xl mx-auto px-6">
          <div className="text-center mb-20">
            <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
              Our Process
            </p>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Photography Process
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Consultation</h3>
              <p className="text-gray-600">
                We discuss your vision, requirements, and goals to plan the perfect photography session.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Planning</h3>
              <p className="text-gray-600">
                We prepare the shoot details, including location, styling, and equipment to ensure everything runs smoothly.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Shooting</h3>
              <p className="text-gray-600">
                Professional photography session with attention to detail, lighting, and composition.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white font-bold text-xl">4</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Delivery</h3>
              <p className="text-gray-600">
                Professionally edited images delivered in high resolution, ready for your marketing needs.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 3D Photography Carousel */}
      <Photography3DCarousel />

      <CleanContact />
      <Footer />
    </div>
  );
}
