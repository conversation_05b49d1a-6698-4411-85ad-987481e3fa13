'use client'

import React from 'react'
import Header from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import HeroPhoto from '@/components/sections/HeroPhoto'
import CleanContact from '@/components/CleanContact'

export default function WebDevelopment() {
  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      <Header />
      
      {/* Web Development Hero */}
      <HeroPhoto 
        title="Web Development"
        subtitle="Modern websites and web applications that drive business growth"
        ctaText="Start Your Project"
        ctaLink="#contact"
        height="large"
        photoQuery="web development coding modern website"
        overlay="gradient"
      />
      
      {/* Service Overview */}
      <section className="py-24 md:py-32 bg-white relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-64 h-64 bg-gray-50 rounded-full opacity-40" />
          <div className="absolute bottom-20 right-20 w-32 h-32 bg-gray-900 opacity-5 rotate-45" />
        </div>

        <div className="relative z-10 max-w-6xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
                What We Build
              </p>
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-8">
                Digital Experiences That Convert
              </h2>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                From responsive websites to complex web applications, we build modern, 
                fast, and user-friendly digital experiences that help your business grow 
                and succeed online.
              </p>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gray-900 rounded-full mr-4"></div>
                  <span className="text-gray-700">Custom website development</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gray-900 rounded-full mr-4"></div>
                  <span className="text-gray-700">E-commerce solutions</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gray-900 rounded-full mr-4"></div>
                  <span className="text-gray-700">Web applications & portals</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gray-900 rounded-full mr-4"></div>
                  <span className="text-gray-700">Mobile-responsive design</span>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl overflow-hidden">
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-4 flex items-center justify-center">
                      <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                      </svg>
                    </div>
                    <p className="text-gray-600 font-medium">Web Development</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Development Services */}
      <section className="py-24 md:py-32 bg-gray-50 relative overflow-hidden">
        <div className="relative z-10 max-w-6xl mx-auto px-6">
          <div className="text-center mb-20">
            <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
              Our Services
            </p>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Development Services
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Custom Websites */}
            <div className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow duration-300">
              <div className="w-12 h-12 bg-gray-900 rounded-xl flex items-center justify-center mb-6">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Custom Websites</h3>
              <p className="text-gray-600 mb-6">
                Bespoke websites built from the ground up to match your brand and 
                business requirements perfectly.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Custom design & development</li>
                <li>• Content management systems</li>
                <li>• SEO optimization</li>
                <li>• Performance optimization</li>
              </ul>
            </div>

            {/* E-commerce */}
            <div className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow duration-300">
              <div className="w-12 h-12 bg-gray-900 rounded-xl flex items-center justify-center mb-6">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 5.5M7 13h10m-10 0L5.4 5M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">E-commerce Solutions</h3>
              <p className="text-gray-600 mb-6">
                Complete online stores with secure payment processing, inventory 
                management, and customer accounts.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Online store development</li>
                <li>• Payment gateway integration</li>
                <li>• Inventory management</li>
                <li>• Order processing systems</li>
              </ul>
            </div>

            {/* Web Applications */}
            <div className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow duration-300">
              <div className="w-12 h-12 bg-gray-900 rounded-xl flex items-center justify-center mb-6">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Web Applications</h3>
              <p className="text-gray-600 mb-6">
                Complex web applications and portals that streamline business 
                processes and improve efficiency.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Custom web applications</li>
                <li>• Database integration</li>
                <li>• User authentication</li>
                <li>• API development</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Technologies */}
      <section className="py-24 md:py-32 bg-white relative overflow-hidden">
        <div className="relative z-10 max-w-6xl mx-auto px-6">
          <div className="text-center mb-20">
            <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
              Technologies
            </p>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Modern Tech Stack
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We use cutting-edge technologies and frameworks to build fast, secure, and scalable web solutions.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-gray-900 font-bold text-sm">React</span>
              </div>
              <p className="text-gray-600 text-sm">React.js</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-gray-900 font-bold text-sm">Next</span>
              </div>
              <p className="text-gray-600 text-sm">Next.js</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-gray-900 font-bold text-sm">TS</span>
              </div>
              <p className="text-gray-600 text-sm">TypeScript</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-gray-900 font-bold text-sm">Node</span>
              </div>
              <p className="text-gray-600 text-sm">Node.js</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-gray-900 font-bold text-sm">AWS</span>
              </div>
              <p className="text-gray-600 text-sm">AWS Cloud</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-gray-900 font-bold text-sm">DB</span>
              </div>
              <p className="text-gray-600 text-sm">Databases</p>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-24 md:py-32 bg-gray-50 relative overflow-hidden">
        <div className="relative z-10 max-w-6xl mx-auto px-6">
          <div className="text-center mb-20">
            <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
              Our Process
            </p>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Development Process
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Discovery</h3>
              <p className="text-gray-600">
                Understanding your business goals, target audience, and technical requirements.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Planning</h3>
              <p className="text-gray-600">
                Creating wireframes, user flows, and technical architecture for your project.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Design</h3>
              <p className="text-gray-600">
                Designing beautiful, user-friendly interfaces that align with your brand.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white font-bold text-xl">4</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Development</h3>
              <p className="text-gray-600">
                Building your website or application with clean, efficient, and scalable code.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gray-900 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white font-bold text-xl">5</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Launch</h3>
              <p className="text-gray-600">
                Testing, optimization, and deployment with ongoing support and maintenance.
              </p>
            </div>
          </div>
        </div>
      </section>

      <CleanContact />
      <Footer />
    </div>
  );
}
