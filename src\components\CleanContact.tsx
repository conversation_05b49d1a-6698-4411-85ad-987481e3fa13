'use client';

import React, { useRef, useEffect, useState } from 'react';
import { motion, useInView } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

interface CleanContactProps {
  title?: string;
  subtitle?: string;
  description?: string;
}

const CleanContact: React.FC<CleanContactProps> = ({
  title = "Let's work together",
  subtitle = "Get in touch",
  description = "Ready to start your next project? We'd love to hear about your vision and discuss how we can help bring it to life."
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const isInView = useInView(containerRef, { once: true, margin: "-100px" });

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  useEffect(() => {
    if (!isInView) return;

    const ctx = gsap.context(() => {
      // Slow, cinematic animation
      gsap.fromTo(titleRef.current,
        {
          opacity: 0,
          y: 30
        },
        {
          opacity: 1,
          y: 0,
          duration: 1.5,
          ease: 'power2.out',
          delay: 0.3
        }
      );

      // Form animation
      const formElements = formRef.current?.querySelectorAll('.form-field');
      if (formElements) {
        gsap.fromTo(formElements,
          {
            opacity: 0,
            y: 20
          },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            stagger: 0.1,
            ease: 'power2.out',
            delay: 0.8
          }
        );
      }
    }, containerRef);

    return () => ctx.revert();
  }, [isInView]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus('success');
      setFormData({ name: '', email: '', company: '', message: '' });
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus('idle'), 3000);
    }
  };

  return (
    <section
      ref={containerRef}
      className="py-24 md:py-32 bg-gray-50 relative overflow-hidden"
    >
      {/* Minimal background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-48 h-48 bg-white rounded-full opacity-60" />
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-gray-900 opacity-5 rotate-45" />
        <div className="absolute top-1/2 left-1/2 w-px h-64 bg-gray-200 transform -translate-x-1/2" />
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-6">
        {/* Clean Header */}
        <div className="text-center mb-16">
          <motion.p
            className="text-sm md:text-base font-medium text-gray-500 tracking-[0.2em] uppercase mb-6"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-50px" }}
            transition={{ delay: 0.2, duration: 1, ease: [0.25, 0.46, 0.45, 0.94] }}
          >
            {subtitle}
          </motion.p>

          <motion.h2
            ref={titleRef}
            className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 mb-6"
            initial={{ opacity: 0, y: 50, scale: 0.95 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 1.2, delay: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
          >
            {title.split(' ').map((word, index) => (
              <motion.span
                key={index}
                className="inline-block mr-4"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{
                  duration: 0.8,
                  delay: 0.5 + index * 0.1,
                  ease: [0.25, 0.46, 0.45, 0.94]
                }}
              >
                {word}
              </motion.span>
            ))}
          </motion.h2>

          <motion.p
            className="text-lg text-gray-600 font-light leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-50px" }}
            transition={{ delay: 0.8, duration: 1, ease: [0.25, 0.46, 0.45, 0.94] }}
          >
            {description}
          </motion.p>
        </div>

        {/* Clean Contact Form */}
        <motion.form 
          ref={formRef} 
          onSubmit={handleSubmit} 
          className="space-y-8"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-50px" }}
          transition={{ duration: 0.8, delay: 1, ease: [0.25, 0.46, 0.45, 0.94] }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div 
              className="form-field"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 1.2, ease: [0.25, 0.46, 0.45, 0.94] }}
            >
              <motion.label 
                htmlFor="name" 
                className="block text-sm font-medium text-gray-700 mb-3"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 1.3 }}
              >
                Name
              </motion.label>
              <motion.input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-0 py-3 border-0 border-b border-gray-300 bg-transparent focus:border-gray-900 focus:outline-none transition-all duration-500 text-gray-900"
                placeholder="Your name"
                initial={{ borderBottomColor: "#d1d5db" }}
                whileFocus={{ 
                  borderBottomColor: "#111827",
                  scale: 1.01,
                  transition: { duration: 0.3 }
                }}
              />
            </motion.div>

            <motion.div 
              className="form-field"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 1.3, ease: [0.25, 0.46, 0.45, 0.94] }}
            >
              <motion.label 
                htmlFor="email" 
                className="block text-sm font-medium text-gray-700 mb-3"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 1.4 }}
              >
                Email
              </motion.label>
              <motion.input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                className="w-full px-0 py-3 border-0 border-b border-gray-300 bg-transparent focus:border-gray-900 focus:outline-none transition-all duration-500 text-gray-900"
                placeholder="<EMAIL>"
                initial={{ borderBottomColor: "#d1d5db" }}
                whileFocus={{ 
                  borderBottomColor: "#111827",
                  scale: 1.01,
                  transition: { duration: 0.3 }
                }}
              />
            </motion.div>
          </div>

          <motion.div 
            className="form-field"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 1.4, ease: [0.25, 0.46, 0.45, 0.94] }}
          >
            <motion.label 
              htmlFor="company" 
              className="block text-sm font-medium text-gray-700 mb-3"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 1.5 }}
            >
              Company
            </motion.label>
            <motion.input
              type="text"
              id="company"
              name="company"
              value={formData.company}
              onChange={handleInputChange}
              className="w-full px-0 py-3 border-0 border-b border-gray-300 bg-transparent focus:border-gray-900 focus:outline-none transition-all duration-500 text-gray-900"
              placeholder="Your company"
              initial={{ borderBottomColor: "#d1d5db" }}
              whileFocus={{ 
                borderBottomColor: "#111827",
                scale: 1.01,
                transition: { duration: 0.3 }
              }}
            />
          </motion.div>

          <motion.div 
            className="form-field"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 1.5, ease: [0.25, 0.46, 0.45, 0.94] }}
          >
            <motion.label 
              htmlFor="message" 
              className="block text-sm font-medium text-gray-700 mb-3"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 1.6 }}
            >
              Message
            </motion.label>
            <motion.textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              required
              rows={6}
              className="w-full px-0 py-3 border-0 border-b border-gray-300 bg-transparent focus:border-gray-900 focus:outline-none transition-all duration-500 text-gray-900 resize-none"
              placeholder="Tell us about your project..."
              initial={{ borderBottomColor: "#d1d5db" }}
              whileFocus={{ 
                borderBottomColor: "#111827",
                scale: 1.01,
                transition: { duration: 0.3 }
              }}
            />
          </motion.div>

          <motion.div 
            className="form-field pt-8"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 1.6, ease: [0.25, 0.46, 0.45, 0.94] }}
          >
            <motion.button
              type="submit"
              disabled={isSubmitting}
              className="group relative px-12 py-4 bg-gray-900 text-white font-medium text-base tracking-wide transition-all duration-500 hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
              initial={{ scale: 0.95, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 1.7, ease: [0.25, 0.46, 0.45, 0.94] }}
              whileHover={{ 
                y: -3, 
                scale: 1.02,
                boxShadow: "0 10px 30px rgba(0, 0, 0, 0.2)",
                transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
              }}
              whileTap={{ y: 0, scale: 0.98 }}
            >
              <motion.span 
                className="relative z-10"
                animate={{
                  scale: isSubmitting ? 0.95 : 1
                }}
                transition={{ duration: 0.3 }}
              >
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </motion.span>

              {/* Clean hover effect */}
              <div className="absolute inset-0 bg-gray-800 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-500" />
            </motion.button>

            {/* Status Messages */}
            {submitStatus === 'success' && (
              <motion.p
                className="mt-4 text-green-600 font-medium"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                Thank you! Your message has been sent.
              </motion.p>
            )}

            {submitStatus === 'error' && (
              <motion.p
                className="mt-4 text-red-600 font-medium"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                Something went wrong. Please try again.
              </motion.p>
            )}
          </motion.div>
        </motion.form>

        {/* Contact Information */}
        <motion.div 
          className="mt-20 pt-16 border-t border-gray-200"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-50px" }}
          transition={{ duration: 0.8, delay: 0.2, ease: [0.25, 0.46, 0.45, 0.94] }}
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
              whileHover={{ 
                y: -5,
                transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
              }}
            >
              <motion.h3 
                className="text-sm font-medium text-gray-900 mb-2 tracking-wider"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                EMAIL
              </motion.h3>
              <motion.p 
                className="text-gray-600 font-light"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <EMAIL>
              </motion.p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }}
              whileHover={{ 
                y: -5,
                transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
              }}
            >
              <motion.h3 
                className="text-sm font-medium text-gray-900 mb-2 tracking-wider"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                PHONE
              </motion.h3>
              <motion.p 
                className="text-gray-600 font-light"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.7 }}
              >
                +352 123 456 789
              </motion.p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }}
              whileHover={{ 
                y: -5,
                transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
              }}
            >
              <motion.h3 
                className="text-sm font-medium text-gray-900 mb-2 tracking-wider"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.7 }}
              >
                LOCATION
              </motion.h3>
              <motion.p 
                className="text-gray-600 font-light"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.8 }}
              >
                Luxembourg City
              </motion.p>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CleanContact;
