'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, useScroll, useTransform, useMotionValue, useSpring } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { useSmoothScroll } from '@/hooks/useSmoothScroll';

gsap.registerPlugin(ScrollTrigger);

interface CleanHeroProps {
  title?: string;
  subtitle?: string;
  description?: string;
  ctaText?: string;
  ctaLink?: string;
  secondaryCtaText?: string;
  secondaryCtaLink?: string;
}

const CleanHero: React.FC<CleanHeroProps> = ({
  title = "We create digital experiences that matter",
  subtitle = "Strategy • Design • Development",
  description = "Transforming ideas into powerful digital solutions that drive growth and create lasting impact for forward-thinking brands.",
  ctaText = "Start Your Project",
  ctaLink = "/contact",
  secondaryCtaText = "View Our Work",
  secondaryCtaLink = "/portfolio"
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLAnchorElement>(null);
  const { scrollToTarget } = useSmoothScroll();

  // Interactive mouse tracking
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const springConfig = { damping: 25, stiffness: 700 };
  const mouseXSpring = useSpring(mouseX, springConfig);
  const mouseYSpring = useSpring(mouseY, springConfig);
  
  // Interactive particles state
  const [particles, setParticles] = useState<Array<{id: number, x: number, y: number, delay: number}>>([]);
  const [isHovered, setIsHovered] = useState(false);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [0, -50]);

  // Mouse tracking effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const x = (e.clientX - rect.left - rect.width / 2) / rect.width;
        const y = (e.clientY - rect.top - rect.height / 2) / rect.height;
        mouseX.set(x * 100);
        mouseY.set(y * 100);
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [mouseX, mouseY]);

  // Generate interactive particles
  useEffect(() => {
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 5
    }));
    setParticles(newParticles);
  }, []);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Ultra-cinematic entrance animation
      const tl = gsap.timeline({ delay: 1.2 });

      // Animate subtitle first with dramatic entrance
      tl.fromTo(subtitleRef.current,
        {
          opacity: 0,
          y: 30,
          scale: 0.9
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 2.5,
          ease: "power3.out"
        }
      )
      // Then animate title words with ultra-slow stagger
      .fromTo(titleRef.current?.children || [],
        {
          opacity: 0,
          y: 80,
          rotationX: 45,
          scale: 0.8
        },
        {
          opacity: 1,
          y: 0,
          rotationX: 0,
          scale: 1,
          duration: 2.8,
          stagger: 0.4,
          ease: "power4.out"
        },
        "-=1.5"
      )
      // Description animation with cinematic timing
      .fromTo(descriptionRef.current,
        {
          opacity: 0,
          y: 40,
          scale: 0.95
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 2.2,
          ease: "power3.out"
        },
        "-=1.2"
      )
      // CTA button animation with dramatic entrance
      .fromTo(ctaRef.current,
        {
          opacity: 0,
          y: 30,
          scale: 0.9
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1.8,
          ease: "power3.out"
        },
        "-=0.8"
      );

      // Subtle hover effect for CTA
      if (ctaRef.current) {
        const ctaElement = ctaRef.current;

        ctaElement.addEventListener('mouseenter', () => {
          gsap.to(ctaElement, {
            y: -2,
            duration: 0.4,
            ease: "power2.out"
          });
        });

        ctaElement.addEventListener('mouseleave', () => {
          gsap.to(ctaElement, {
            y: 0,
            duration: 0.4,
            ease: "power2.out"
          });
        });
      }
    }, containerRef);

    return () => ctx.revert();
  }, []);

  const handleCTAClick = (e: React.MouseEvent) => {
    e.preventDefault();
    scrollToTarget(ctaLink);
  };

  // Split title into words for animation
  const titleWords = title.split(' ');

  return (
    <motion.section
      ref={containerRef}
      className="relative min-h-screen flex items-start justify-center overflow-hidden pt-32 lg:pt-40 pb-24"
      style={{
        y,
        backgroundColor: '#122842'
      }}
    >
      {/* Interactive Background Elements */}
      <div className="absolute inset-0 z-0">
        {/* Mouse-following gradient */}
        <motion.div
          className="absolute inset-0 opacity-30"
          style={{
            background: `radial-gradient(600px circle at ${mouseXSpring}px ${mouseYSpring}px, rgba(56, 189, 248, 0.15), transparent 40%)`,
            x: mouseXSpring,
            y: mouseYSpring
          }}
        />

        {/* Enhanced Interactive particle system */}
        {particles.map((particle, index) => (
          <motion.div
            key={particle.id}
            className="absolute rounded-full"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: Math.random() * 3 + 1,
              height: Math.random() * 3 + 1,
              background: `radial-gradient(circle, ${['#38bdf8', '#06b6d4', '#0891b2', '#ffffff'][Math.floor(Math.random() * 4)]} 0%, transparent 70%)`
            }}
            animate={{
              opacity: [0, 0.9, 0],
              scale: [0.5, 1.8, 0.5],
              y: [-20, -100, -20],
              rotate: [0, 360]
            }}
            transition={{
              duration: 8 + Math.random() * 4,
              repeat: Infinity,
              delay: particle.delay,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Floating geometric shapes */}
        {Array.from({ length: 8 }).map((_, index) => (
          <motion.div
            key={`shape-${index}`}
            className="absolute border border-cyan-300/20 backdrop-blur-sm"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: Math.random() * 60 + 20,
              height: Math.random() * 60 + 20,
              borderRadius: index % 3 === 0 ? '50%' : index % 3 === 1 ? '0%' : '20%',
              background: `linear-gradient(45deg, rgba(56, 189, 248, 0.05), rgba(6, 182, 212, 0.05))`
            }}
            animate={{
              y: [0, -30, 0],
              x: [0, Math.sin(index) * 20, 0],
              rotate: [0, 360],
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.7, 0.3]
            }}
            transition={{
              duration: 15 + index * 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: index * 0.5
            }}
          />
        ))}

        {/* Dynamic grid pattern */}
        <motion.div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `
              linear-gradient(rgba(56, 189, 248, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(56, 189, 248, 0.3) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            x: mouseXSpring,
            y: mouseYSpring
          }}
          animate={{
            opacity: isHovered ? 0.2 : 0.05
          }}
          transition={{ duration: 0.3 }}
        />

        {/* Enhanced Animated light beams with depth */}
        {Array.from({ length: 6 }).map((_, index) => (
          <motion.div
            key={`beam-${index}`}
            className="absolute top-0 w-px h-full opacity-20"
            style={{
              left: `${20 + index * 15}%`,
              background: `linear-gradient(to bottom, transparent 0%, ${['#38bdf8', '#06b6d4', '#0891b2', '#67e8f9', '#a5f3fc', '#ffffff'][index]} 30%, ${['#38bdf8', '#06b6d4', '#0891b2', '#67e8f9', '#a5f3fc', '#ffffff'][index]} 70%, transparent 100%)`,
              filter: 'blur(0.5px)'
            }}
            animate={{
              opacity: [0.1, 0.5, 0.1],
              scaleY: [0.6, 1.4, 0.6],
              x: [0, Math.sin(index) * 10, 0]
            }}
            transition={{
              duration: 8 + index * 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: index * 0.8
            }}
          />
        ))}

        {/* Cinematic spotlight effects */}
        <motion.div
          className="absolute top-1/4 left-1/2 transform -translate-x-1/2 w-96 h-96 rounded-full opacity-10"
          style={{
            background: 'radial-gradient(circle, rgba(56, 189, 248, 0.3) 0%, transparent 70%)',
            filter: 'blur(40px)'
          }}
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.05, 0.15, 0.05]
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Secondary spotlight */}
        <motion.div
          className="absolute bottom-1/3 right-1/4 w-64 h-64 rounded-full opacity-8"
          style={{
            background: 'radial-gradient(circle, rgba(6, 182, 212, 0.2) 0%, transparent 70%)',
            filter: 'blur(30px)'
          }}
          animate={{
            scale: [0.8, 1.2, 0.8],
            opacity: [0.03, 0.12, 0.03],
            x: [0, 20, 0],
            y: [0, -15, 0]
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 3
          }}
        />

        {/* Interactive geometric shapes */}
        <motion.div
          className="absolute bottom-1/4 left-1/6 w-32 h-32 opacity-20 border border-blue-400"
          style={{
            rotate: mouseXSpring,
            scale: 1 + Math.abs(mouseYSpring.get()) * 0.002
          }}
          animate={{
            rotate: [0, 360]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        <motion.div
          className="absolute top-1/3 right-1/5 w-24 h-24 opacity-15 border border-cyan-300 rounded-full"
          style={{
            x: mouseXSpring,
            y: mouseYSpring,
            scale: 1 + Math.abs(mouseXSpring.get()) * 0.003
          }}
          animate={{
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Enhanced Cinematic vignette with depth */}
        <div
          className="absolute inset-0 opacity-70"
          style={{
            background: 'radial-gradient(ellipse at center, transparent 0%, rgba(18, 40, 66, 0.3) 40%, rgba(18, 40, 66, 0.8) 80%, #122842 100%)'
          }}
        />

        {/* Depth-of-field blur layers */}
        <motion.div
          className="absolute inset-0 opacity-30"
          style={{
            background: 'radial-gradient(circle at 30% 20%, rgba(56, 189, 248, 0.1) 0%, transparent 50%), radial-gradient(circle at 70% 80%, rgba(6, 182, 212, 0.08) 0%, transparent 50%)',
            filter: 'blur(60px)'
          }}
          animate={{
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Atmospheric particles */}
        {Array.from({ length: 15 }).map((_, index) => (
          <motion.div
            key={`atmosphere-${index}`}
            className="absolute rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: Math.random() * 2 + 0.5,
              height: Math.random() * 2 + 0.5,
              background: `rgba(${Math.random() > 0.5 ? '56, 189, 248' : '255, 255, 255'}, ${Math.random() * 0.3 + 0.1})`,
              filter: `blur(${Math.random() * 2 + 0.5}px)`
            }}
            animate={{
              y: [0, -100],
              x: [0, Math.sin(index) * 30],
              opacity: [0, 0.6, 0],
              scale: [0.5, 1.2, 0.5]
            }}
            transition={{
              duration: Math.random() * 20 + 15,
              repeat: Infinity,
              ease: "linear",
              delay: Math.random() * 10
            }}
          />
        ))}
      </div>

      <motion.div 
        className="relative z-30 w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Cinematic Subtitle - Only show if subtitle exists */}
        {subtitle && (
          <motion.div className="overflow-hidden mb-8 sm:mb-12 lg:mb-16">
            <motion.p
              ref={subtitleRef}
              className="text-xs sm:text-sm md:text-base font-light text-gray-400 tracking-[0.2em] sm:tracking-[0.3em] md:tracking-[0.4em] uppercase"
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-50px" }}
              transition={{ duration: 1.2, delay: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }}
            >
              {subtitle}
            </motion.p>
          </motion.div>
        )}

        {/* Enhanced Responsive Main Title with immersive effects */}
        <motion.div 
          className="mb-12 sm:mb-16 lg:mb-20 relative"
          style={{
            y: useTransform(scrollY, [0, 500], [0, -50])
          }}
        >
          {/* Title glow background */}
          <motion.div
            className="absolute inset-0 -z-10"
            style={{
              background: 'radial-gradient(ellipse at center, rgba(56, 189, 248, 0.1) 0%, transparent 70%)',
              filter: 'blur(40px)'
            }}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          
          <h1
            ref={titleRef}
            className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl 2xl:text-9xl font-black text-white leading-[0.85] sm:leading-[0.8] tracking-tight relative z-10"
            style={{
              fontFamily: 'Inter, system-ui, sans-serif',
              fontWeight: 900,
              letterSpacing: '-0.01em'
            }}
          >
            {titleWords.map((word, index) => (
              <motion.span
                key={index}
                className="inline-block mr-2 sm:mr-3 md:mr-4 lg:mr-6 xl:mr-8 cursor-default relative"
                style={{
                  textShadow: '0 0 40px rgba(255, 255, 255, 0.1), 0 0 80px rgba(56, 189, 248, 0.2)'
                }}
                whileHover={{
                  scale: 1.05,
                  textShadow: '0 0 60px rgba(56, 189, 248, 0.4), 0 0 120px rgba(56, 189, 248, 0.3)',
                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                }}
                animate={{
                  y: isHovered ? Math.sin(index * 0.5) * 5 : 0,
                  textShadow: [
                    '0 0 40px rgba(255, 255, 255, 0.1), 0 0 80px rgba(56, 189, 248, 0.2)',
                    '0 0 50px rgba(255, 255, 255, 0.2), 0 0 100px rgba(56, 189, 248, 0.3)',
                    '0 0 40px rgba(255, 255, 255, 0.1), 0 0 80px rgba(56, 189, 248, 0.2)'
                  ]
                }}
                transition={{
                  duration: 0.5,
                  delay: index * 0.1,
                  textShadow: {
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: index * 0.2
                  }
                }}
              >
                {/* Individual letter shimmer effect */}
                <motion.span
                  className="relative"
                  animate={{
                    background: [
                      'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%)',
                      'linear-gradient(90deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%)',
                      'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%)'
                    ]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: index * 0.3
                  }}
                  style={{
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text'
                  }}
                >
                  {word}
                </motion.span>
              </motion.span>
            ))}
          </h1>
        </motion.div>

        {/* Enhanced Responsive Description with parallax */}
        <motion.div 
          className="overflow-hidden mb-16 sm:mb-20 lg:mb-24 relative"
          style={{
            y: useTransform(scrollY, [0, 500], [0, -30])
          }}
        >
          {/* Description background glow */}
          <motion.div
            className="absolute inset-0 -z-10"
            style={{
              background: 'radial-gradient(ellipse at center, rgba(6, 182, 212, 0.05) 0%, transparent 70%)',
              filter: 'blur(60px)'
            }}
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.2, 0.4, 0.2]
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          />
          
          <motion.p
            ref={descriptionRef}
            className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-300 max-w-xs sm:max-w-2xl lg:max-w-4xl mx-auto leading-relaxed font-light tracking-wide px-4 sm:px-0 relative z-10"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-50px" }}
            transition={{ duration: 1, delay: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
            style={{
              lineHeight: '1.6',
              letterSpacing: '0.01em',
              textShadow: '0 0 20px rgba(255, 255, 255, 0.1)'
            }}
            animate={{
              textShadow: [
                '0 0 20px rgba(255, 255, 255, 0.1)',
                '0 0 30px rgba(255, 255, 255, 0.15)',
                '0 0 20px rgba(255, 255, 255, 0.1)'
              ]
            }}
            transition={{
              textShadow: {
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut"
              }
            }}
          >
            {description}
          </motion.p>
        </motion.div>

        {/* Enhanced Responsive CTA Buttons with immersive effects */}
        <motion.div 
          className="flex flex-col sm:flex-row gap-6 sm:gap-8 lg:gap-12 justify-center items-center mb-16 sm:mb-20 lg:mb-24 relative"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-50px" }}
          transition={{ duration: 1, delay: 1, ease: [0.25, 0.46, 0.45, 0.94] }}
          style={{
            y: useTransform(scrollY, [0, 500], [0, -20])
          }}
        >
          {/* CTA background glow */}
          <motion.div
            className="absolute inset-0 -z-10"
            style={{
              background: 'radial-gradient(ellipse at center, rgba(56, 189, 248, 0.08) 0%, transparent 70%)',
              filter: 'blur(80px)'
            }}
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          />
          <motion.a
            ref={ctaRef}
            href={ctaLink}
            onClick={handleCTAClick}
            className="group relative px-8 sm:px-12 lg:px-16 py-4 sm:py-5 lg:py-6 bg-white text-black font-bold text-sm sm:text-base lg:text-lg tracking-[0.1em] uppercase transition-all duration-700 hover:bg-gray-100 overflow-hidden w-full sm:w-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.8, duration: 1.2 }}
            whileHover={{
              scale: 1.05,
              y: -3,
              boxShadow: '0 20px 40px rgba(56, 189, 248, 0.3)',
              transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
            }}
            whileTap={{ scale: 0.95 }}
            style={{
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.1)'
            }}
          >
            <span className="relative z-10">{ctaText}</span>

            {/* Interactive ripple effect */}
            <motion.div 
              className="absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 opacity-0 group-hover:opacity-20"
              initial={{ scale: 0, opacity: 0 }}
              whileHover={{ scale: 1, opacity: 0.2 }}
              transition={{ duration: 0.3 }}
            />
            
            {/* Cinematic slide effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-gray-100 to-white transform -translate-x-full group-hover:translate-x-0 transition-transform duration-700" />
          </motion.a>

          {secondaryCtaText && secondaryCtaLink && (
            <motion.a
              href={secondaryCtaLink}
              className="group text-gray-400 hover:text-white font-medium text-sm sm:text-base lg:text-lg tracking-[0.1em] uppercase transition-all duration-700 relative"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2.2, duration: 1.2 }}
              whileHover={{
                scale: 1.05,
                y: -2,
                color: '#38bdf8',
                transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
              }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="relative z-10">{secondaryCtaText}</span>
              <motion.div 
                className="absolute bottom-0 left-0 h-px bg-gradient-to-r from-blue-400 to-cyan-400"
                initial={{ width: 0 }}
                whileHover={{ width: '100%' }}
                transition={{ duration: 0.3 }}
              />
            </motion.a>
          )}
        </motion.div>
      </motion.div>

      {/* Responsive Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 sm:bottom-12 lg:bottom-16 left-1/2 transform -translate-x-1/2 z-30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 3.5, duration: 1.5 }}
      >
        <div className="flex flex-col items-center text-gray-500">
          <span className="text-xs font-light mb-4 sm:mb-6 tracking-[0.3em] uppercase opacity-60">Scroll</span>
          <motion.div
            className="w-px h-16 sm:h-20 bg-gradient-to-b from-gray-500 via-gray-400 to-transparent"
            animate={{
              scaleY: [1, 1.4, 1],
              opacity: [0.4, 0.8, 0.4]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="w-1 h-1 bg-gray-400 rounded-full mt-2"
            animate={{
              y: [0, 10, 0],
              opacity: [0.6, 1, 0.6]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 0.5
            }}
          />
        </div>
      </motion.div>
    </motion.section>
  );
};

export default CleanHero;
