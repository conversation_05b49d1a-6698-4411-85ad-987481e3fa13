'use client';

import React, { useEffect, useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { useSmoothScroll } from '@/hooks/useSmoothScroll';
import { Particles } from 'react-tsparticles';

gsap.registerPlugin(ScrollTrigger);

interface InspiredHeroProps {
  title?: string;
  subtitle?: string;
  description?: string;
  ctaText?: string;
  ctaLink?: string;
  secondaryCtaText?: string;
  secondaryCtaLink?: string;
}

const InspiredHero: React.FC<InspiredHeroProps> = ({
  title = "We create digital experiences that matter",
  subtitle = "Strategy • Design • Development",
  description = "Transforming ideas into powerful digital solutions that drive growth and create lasting impact for forward-thinking brands.",
  ctaText = "Start Your Project",
  ctaLink = "/contact",
  secondaryCtaText,
  secondaryCtaLink
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLAnchorElement>(null);
  const { scrollToSection } = useSmoothScroll();
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });
  
  const y = useTransform(scrollYProgress, [0, 1], [0, -100]);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0]);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Initial animation sequence
      const tl = gsap.timeline({ delay: 0.5 });
      
      // Animate subtitle first
      tl.fromTo(subtitleRef.current, 
        { 
          opacity: 0, 
          y: 30,
          letterSpacing: "0.5em"
        },
        { 
          opacity: 1, 
          y: 0,
          letterSpacing: "0.2em",
          duration: 1.2,
          ease: "power3.out"
        }
      )
      // Then animate title with stagger effect
      .fromTo(titleRef.current?.children || [], 
        { 
          opacity: 0, 
          y: 100,
          rotationX: 90
        },
        { 
          opacity: 1, 
          y: 0,
          rotationX: 0,
          duration: 1.5,
          stagger: 0.1,
          ease: "power4.out"
        }, 
        "-=0.8"
      )
      // Description animation
      .fromTo(descriptionRef.current, 
        { 
          opacity: 0, 
          y: 50
        },
        { 
          opacity: 1, 
          y: 0,
          duration: 1,
          ease: "power3.out"
        }, 
        "-=0.5"
      )
      // CTA button animation
      .fromTo(ctaRef.current, 
        { 
          opacity: 0, 
          scale: 0.8,
          y: 30
        },
        { 
          opacity: 1, 
          scale: 1,
          y: 0,
          duration: 0.8,
          ease: "back.out(1.7)"
        }, 
        "-=0.3"
      );

      // Hover animations for CTA
      if (ctaRef.current) {
        const ctaElement = ctaRef.current;
        
        ctaElement.addEventListener('mouseenter', () => {
          gsap.to(ctaElement, {
            scale: 1.05,
            duration: 0.3,
            ease: "power2.out"
          });
        });
        
        ctaElement.addEventListener('mouseleave', () => {
          gsap.to(ctaElement, {
            scale: 1,
            duration: 0.3,
            ease: "power2.out"
          });
        });
      }
    }, containerRef);

    return () => ctx.revert();
  }, []);

  const handleCTAClick = (e: React.MouseEvent) => {
    e.preventDefault();
    scrollToSection(ctaLink);
  };

  // Split title into words for animation
  const titleWords = title.split(' ');

  return (
    <motion.section
      ref={containerRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"
      style={{ y, opacity }}
    >
      {/* Optimized Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Optimized animated blobs - using transform instead of blur */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full animate-blob will-change-transform"></div>
        <div className="absolute top-1/3 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full animate-blob animation-delay-2000 will-change-transform"></div>
        <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-pink-500/10 rounded-full animate-blob animation-delay-4000 will-change-transform"></div>
        
        {/* Optimized geometric shapes */}
        <div className="absolute top-20 right-20 w-32 h-32 border border-gray-400/20 rounded-lg rotate-12 animate-float will-change-transform"></div>
        <div className="absolute bottom-32 left-16 w-24 h-24 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full animate-scale-pulse will-change-transform"></div>
        <div className="absolute top-1/2 right-1/3 w-16 h-16 border-2 border-gray-400/20 rotate-45 animate-rotate will-change-transform"></div>
        
        {/* Minimal floating elements */}
        <div className="absolute top-40 left-1/2 w-2 h-2 bg-gray-400/40 rounded-full animate-pulse will-change-transform"></div>
        <div className="absolute bottom-40 right-1/2 w-3 h-3 bg-blue-400/30 rounded-full animate-bounce will-change-transform"></div>
      </div>

      {/* Grid Pattern */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-6 text-center">
        {/* Subtitle */}
        <motion.p
          ref={subtitleRef}
          className="text-sm md:text-base font-medium text-gray-300 tracking-[0.2em] uppercase mb-8 font-['Inter']" // Add font import if needed
          initial={{ opacity: 0 }}
        >
          {subtitle}
        </motion.p>

        {/* Main Title */}
        <h1
          ref={titleRef}
          className="text-5xl md:text-7xl lg:text-8xl xl:text-9xl font-bold text-white leading-[0.85] mb-8 font-['Inter']"
        >
          {titleWords.map((word, index) => (
            <span
              key={index}
              className="inline-block mr-4 md:mr-6 perspective-1000"
              style={{ transformStyle: 'preserve-3d' }}
            >
              {word}
            </span>
          ))}
        </h1>

        {/* Description */}
        <motion.p
          ref={descriptionRef}
          className="text-xl md:text-2xl lg:text-3xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-12 font-['Inter']"
          initial={{ opacity: 0 }}
        >
          {description}
        </motion.p>

        {/* CTA Button */}
        <motion.a
          ref={ctaRef}
          href={ctaLink}
          onClick={handleCTAClick}
          className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold text-lg rounded-full hover:from-blue-500 hover:to-purple-500 transition-all duration-300 group cursor-pointer font-['Inter']"
          initial={{ opacity: 0 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {ctaText}
          <svg
            className="ml-3 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 8l4 4m0 0l-4 4m4-4H3"
            />
          </svg>
        </motion.a>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2, duration: 0.8 }}
      >
        <div className="flex flex-col items-center text-gray-400">
          <span className="text-sm font-medium mb-2 tracking-wider">SCROLL</span>
          <motion.div
            className="w-px h-12 bg-gray-300"
            animate={{ scaleY: [1, 1.5, 1] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          />
        </div>
      </motion.div>
    </motion.section>
  );
};

export default InspiredHero;
<Particles
  options={{
    particles: {
      number: { value: 20 },
      color: { value: '#ffffff' },
      shape: { type: 'circle' },
      opacity: { value: 0.2 },
      size: { value: 1 },
      move: { 
        enable: true, 
        speed: 0.3,
        direction: 'none',
        random: false,
        straight: false,
        outModes: {
          default: 'out'
        }
      }
    },
    detectRetina: true,
    fpsLimit: 60
  }}
/>
{/* Optimized Background Gradient */}
<div className="absolute inset-0 bg-gradient-to-br from-gray-900/50 via-blue-900/20 to-purple-900/30 opacity-60"></div>
      </div>
      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2, duration: 0.8 }}
      >
        <div className="flex flex-col items-center text-gray-400">
          <span className="text-sm font-medium mb-2 tracking-wider">SCROLL</span>
          <motion.div
            className="w-px h-12 bg-gray-300"
            animate={{ scaleY: [1, 1.5, 1] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          />
        </div>
      </motion.div>
    </motion.section>
  );
};

export default InspiredHero;