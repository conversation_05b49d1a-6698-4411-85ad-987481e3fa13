'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, useInView } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

interface Service {
  title: string;
  description: string;
  image: string;
  link?: string;
}

interface InspiredServicesProps {
  title?: string;
  subtitle?: string;
  services?: Service[];
}

const defaultServices: Service[] = [
  {
    title: "Content Creation",
    description: "Strategic content development including copywriting, video production, and social media content that engages your audience and drives results.",
    image: "https://images.unsplash.com/photo-1455390582262-044cdead277a?w=500&h=350&fit=crop&crop=entropy&auto=format&q=80",
    link: "/services/content-creation"
  },
  {
    title: "Photography",
    description: "Professional photography services including product photography, corporate headshots, and brand imagery that tells your story.",
    image: "https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=500&h=350&fit=crop&crop=entropy&auto=format&q=80",
    link: "/services/photography"
  },
  {
    title: "Website Development",
    description: "Custom websites and web applications built with modern technologies, optimized for performance, SEO, and user experience.",
    image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=500&h=350&fit=crop&crop=entropy&auto=format&q=80",
    link: "/services/website-development"
  }
];

const InspiredServices: React.FC<InspiredServicesProps> = ({
  title = "Our Expertise",
  subtitle = "We craft digital experiences that drive results",
  services = defaultServices
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const servicesRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(containerRef, { once: true, margin: "-100px" });
  const [hoveredService, setHoveredService] = useState<number | null>(null);



  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.fromTo(titleRef.current,
        {
          opacity: 0,
          y: 50
        },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: titleRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Subtitle animation
      gsap.fromTo(subtitleRef.current,
        {
          opacity: 0,
          y: 30
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          delay: 0.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: subtitleRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Services animation
      const serviceCards = servicesRef.current?.children;
      if (serviceCards) {
        gsap.fromTo(serviceCards,
          {
            opacity: 0,
            y: 80,
            scale: 0.9
          },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 1.2,
            stagger: 0.2,
            ease: "power3.out",
            scrollTrigger: {
              trigger: servicesRef.current,
              start: "top 80%",
              end: "bottom 20%",
              toggleActions: "play none none reverse"
            }
          }
        );
      }

      // Hover animations for service cards
      if (serviceCards) {
        Array.from(serviceCards).forEach((card) => {
          const element = card as HTMLElement;
          
          element.addEventListener('mouseenter', () => {
            gsap.to(element, {
              y: -10,
              scale: 1.02,
              duration: 0.3,
              ease: "power2.out"
            });
          });
          
          element.addEventListener('mouseleave', () => {
            gsap.to(element, {
              y: 0,
              scale: 1,
              duration: 0.3,
              ease: "power2.out"
            });
          });
        });
      }
    }, containerRef);

    return () => ctx.revert();
  }, []);

  return (
    <section
      ref={containerRef}
      className="py-24 md:py-32 bg-white relative overflow-hidden"
    >
      {/* Organic Textured Background */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Main organic shapes */}
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 1200 800" preserveAspectRatio="xMidYMid slice">
          <defs>
            <pattern id="organicTexture" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
              <circle cx="20" cy="20" r="1" fill="#f3f4f6" opacity="0.3"/>
              <circle cx="60" cy="40" r="0.5" fill="#e5e7eb" opacity="0.4"/>
              <circle cx="80" cy="70" r="1.5" fill="#f9fafb" opacity="0.2"/>
              <circle cx="30" cy="80" r="0.8" fill="#f3f4f6" opacity="0.5"/>
            </pattern>
            <linearGradient id="organicGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#f8fafc" stopOpacity="0.8"/>
              <stop offset="50%" stopColor="#e2e8f0" stopOpacity="0.4"/>
              <stop offset="100%" stopColor="#cbd5e1" stopOpacity="0.2"/>
            </linearGradient>
            <linearGradient id="organicGradient2" x1="100%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#fef7ff" stopOpacity="0.6"/>
              <stop offset="50%" stopColor="#f3e8ff" stopOpacity="0.3"/>
              <stop offset="100%" stopColor="#e9d5ff" stopOpacity="0.1"/>
            </linearGradient>
          </defs>
          
          {/* Large organic blob top-right */}
          <path d="M800,0 C950,50 1100,150 1200,300 C1200,200 1200,100 1200,0 Z" fill="url(#organicGradient1)"/>
          
          {/* Flowing shape left side */}
          <path d="M0,200 C150,180 250,250 300,400 C250,500 150,600 0,650 Z" fill="url(#organicGradient2)"/>
          
          {/* Central organic element */}
          <path d="M400,300 C500,250 600,280 700,350 C650,450 550,480 450,420 C380,380 350,340 400,300 Z" fill="url(#organicTexture)" opacity="0.3"/>
          
          {/* Bottom flowing shape */}
          <path d="M600,600 C750,580 900,650 1200,700 C1200,750 1200,800 1000,800 C800,800 700,750 600,600 Z" fill="url(#organicGradient1)" opacity="0.4"/>
          
          {/* Small organic details */}
          <ellipse cx="200" cy="150" rx="80" ry="40" fill="#f1f5f9" opacity="0.3" transform="rotate(25 200 150)"/>
          <ellipse cx="900" cy="500" rx="60" ry="30" fill="#faf5ff" opacity="0.4" transform="rotate(-15 900 500)"/>
          <ellipse cx="150" cy="700" rx="40" ry="20" fill="#f8fafc" opacity="0.5" transform="rotate(45 150 700)"/>
        </svg>
        
        {/* Subtle noise texture overlay */}
        <div className="absolute inset-0 opacity-20" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.1'/%3E%3C/svg%3E")`,
          backgroundSize: '200px 200px'
        }}></div>
        
        {/* Floating organic particles */}
        <div className="absolute top-1/4 left-1/3 w-2 h-2 bg-slate-300 rounded-full opacity-40 animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-purple-200 rounded-full opacity-50 animate-pulse" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-1/4 left-1/5 w-3 h-3 bg-blue-100 rounded-full opacity-30 animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 right-1/3 w-1.5 h-1.5 bg-gray-200 rounded-full opacity-45 animate-pulse" style={{animationDelay: '0.5s'}}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-20">
          <motion.h2
            ref={titleRef}
            className="text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-gray-900 mb-6"
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 1, ease: "easeOut" }}
          >
            {title}
          </motion.h2>
          
          <motion.p
            ref={subtitleRef}
            className="text-2xl md:text-3xl lg:text-4xl text-gray-600 max-w-4xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
          >
            {subtitle}
          </motion.p>
        </div>

        {/* Services Vertical Stack */}
        <div className="max-w-7xl mx-auto relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 lg:gap-32">
            {/* Left Column - Service List */}
            <div ref={servicesRef} className="space-y-16">
              {services.map((service, index) => (
                <motion.div
                  key={index}
                  className="group border-b border-gray-200 pb-8 last:border-b-0"
                  style={{
                    cursor: `url("data:image/svg+xml,%3csvg width='60' height='60' xmlns='http://www.w3.org/2000/svg'%3e%3cdefs%3e%3cfilter id='shadow' x='-50%25' y='-50%25' width='200%25' height='200%25'%3e%3cfeDropShadow dx='0' dy='2' stdDeviation='4' flood-color='%23000000' flood-opacity='0.2'/%3e%3c/filter%3e%3c/defs%3e%3ccircle cx='30' cy='30' r='26' fill='%232563eb' filter='url(%23shadow)'/%3e%3cpath d='M20 30h16m-6-6l6 6-6 6' stroke='%23ffffff' stroke-width='2.5' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e") 30 30, pointer`
                  }}
                  initial={{ opacity: 0, y: 50 }}
                  animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
                  transition={{ duration: 0.8, delay: index * 0.1, ease: "easeOut" }}
                  onMouseEnter={() => setHoveredService(index)}
                  onMouseLeave={() => setHoveredService(null)}
                >
                  <div className="flex items-center">
                    <div className="flex-1">
                      <h3 className={`text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold transition-all duration-500 whitespace-nowrap ${
                        hoveredService === index 
                          ? 'text-gray-900 transform translate-x-6' 
                          : 'text-gray-400 hover:text-gray-600'
                      }`}>
                        {service.title}
                      </h3>
                    </div>
                  </div>
                  
                  <motion.div
                    className="overflow-hidden"
                    initial={{ height: 0 }}
                    animate={{ 
                      height: hoveredService === index ? 'auto' : 0
                    }}
                    transition={{ duration: 0.4, ease: "easeInOut" }}
                  >
                    <div className="flex items-start gap-6 pt-4 pb-2">
                      <motion.div
                         className="w-40 h-28 rounded-xl overflow-hidden shadow-xl flex-shrink-0"
                         initial={{ opacity: 0, scale: 0.8, x: -20 }}
                         animate={{ 
                           opacity: hoveredService === index ? 1 : 0,
                           scale: hoveredService === index ? 1 : 0.8,
                           x: hoveredService === index ? 0 : -20
                         }}
                         transition={{ duration: 0.4, delay: 0.1, ease: "easeOut" }}
                       >
                        <img
                          src={service.image}
                          alt={service.title}
                          className="w-full h-full object-cover"
                        />
                      </motion.div>
                      
                      <motion.p 
                         className="text-xl text-gray-600 flex-1 leading-relaxed"
                         initial={{ opacity: 0, y: 10 }}
                         animate={{ 
                           opacity: hoveredService === index ? 1 : 0,
                           y: hoveredService === index ? 0 : 10
                         }}
                         transition={{ duration: 0.3, delay: 0.2 }}
                       >
                        {service.description}
                      </motion.p>
                    </div>
                  </motion.div>
                </motion.div>
              ))}
            </div>
            
            {/* Right Column - Decorative Element */}
            <div className="hidden lg:flex items-center justify-center">
              <motion.div
                className="relative w-96 h-96 flex items-center justify-center"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                transition={{ duration: 1, delay: 0.5 }}
              >
                {/* Background Circle */}
                <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full opacity-20"></div>
                
                {/* Decorative Elements */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-32 h-32 border-2 border-gray-300 rounded-full opacity-30"></div>
                </div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-48 h-48 border border-gray-200 rounded-full opacity-20"></div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-20"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 1, delay: 1, ease: "easeOut" }}
        >
          <p className="text-lg text-gray-600 mb-8">
            Ready to transform your digital presence?
          </p>
          <motion.button
            className="inline-flex items-center px-8 py-4 bg-gray-900 text-white font-semibold text-lg rounded-full hover:bg-gray-800 transition-all duration-300 group"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Get Started
            <svg
              className="ml-3 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 8l4 4m0 0l-4 4m4-4H3"
              />
            </svg>
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default InspiredServices;