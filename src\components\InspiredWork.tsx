'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, useInView } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { fetchUnsplashPhotos, getOptimizedImageUrl } from '@/lib/unsplash';

gsap.registerPlugin(ScrollTrigger);

interface Project {
  id: string;
  title: string;
  category: string;
  description: string;
  tags: string[];
  image: string;
  year: string;
}

interface InspiredWorkProps {
  title?: string;
  subtitle?: string;
  maxProjects?: number;
}

const defaultProjects: Project[] = [
  {
    id: '1',
    title: 'Digital Banking Platform',
    category: 'Fintech',
    description: 'A comprehensive digital banking solution that revolutionizes how customers interact with their finances.',
    tags: ['Strategy', 'UX/UI', 'Development'],
    image: '',
    year: '2024'
  },
  {
    id: '2',
    title: 'E-commerce Marketplace',
    category: 'Retail',
    description: 'Multi-vendor marketplace connecting local artisans with global customers through seamless digital experiences.',
    tags: ['E-commerce', 'Mobile', 'Analytics'],
    image: '',
    year: '2024'
  },
  {
    id: '3',
    title: 'Healthcare Management System',
    category: 'Healthcare',
    description: 'Streamlining patient care through intelligent automation and intuitive interface design.',
    tags: ['Healthcare', 'Dashboard', 'Integration'],
    image: '',
    year: '2023'
  },
  {
    id: '4',
    title: 'Sustainable Energy Platform',
    category: 'Energy',
    description: 'Empowering communities to track and optimize their renewable energy consumption.',
    tags: ['Sustainability', 'Data Viz', 'IoT'],
    image: '',
    year: '2023'
  }
];

const InspiredWork: React.FC<InspiredWorkProps> = ({
  title = "Selected work",
  subtitle = "Crafting digital experiences that drive results",
  maxProjects = 4
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const projectsRef = useRef<HTMLDivElement>(null);
  const [projects, setProjects] = useState<Project[]>(defaultProjects);
  const [hoveredProject, setHoveredProject] = useState<string | null>(null);
  const isInView = useInView(containerRef, { once: true, margin: "-100px" });

  useEffect(() => {
    const loadImages = async () => {
      try {
        const photos = await fetchUnsplashPhotos({
          query: 'modern technology business',
          per_page: maxProjects
        });
        
        const updatedProjects = defaultProjects.slice(0, maxProjects).map((project, index) => ({
          ...project,
          image: photos[index] ? getOptimizedImageUrl(photos[index], { width: 800, height: 600, quality: 85 }) : ''
        }));
        
        setProjects(updatedProjects);
      } catch (error) {
        console.error('Error loading images:', error);
      }
    };

    loadImages();
  }, [maxProjects]);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.fromTo(titleRef.current,
        {
          opacity: 0,
          y: 50
        },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: titleRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Subtitle animation
      gsap.fromTo(subtitleRef.current,
        {
          opacity: 0,
          y: 30
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          delay: 0.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: subtitleRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Projects animation
      const projectCards = projectsRef.current?.children;
      if (projectCards) {
        gsap.fromTo(projectCards,
          {
            opacity: 0,
            y: 100,
            scale: 0.9
          },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 1.2,
            stagger: 0.15,
            ease: "power3.out",
            scrollTrigger: {
              trigger: projectsRef.current,
              start: "top 80%",
              end: "bottom 20%",
              toggleActions: "play none none reverse"
            }
          }
        );
      }
    }, containerRef);

    return () => ctx.revert();
  }, [projects]);

  return (
    <section
      ref={containerRef}
      className="py-24 md:py-32 bg-gray-50 relative overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-0 w-96 h-96 bg-gradient-to-r from-blue-100 to-transparent rounded-full transform -translate-x-1/2"></div>
        <div className="absolute bottom-1/4 right-0 w-96 h-96 bg-gradient-to-l from-purple-100 to-transparent rounded-full transform translate-x-1/2"></div>
        
        {/* Geometric patterns */}
        <div className="absolute top-10 left-10 w-40 h-40 border border-gray-100 rounded-full opacity-30"></div>
        <div className="absolute bottom-10 right-10 w-56 h-56 bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg rotate-6 opacity-40"></div>
        <div className="absolute top-1/2 right-1/5 w-24 h-24 border-2 border-dashed border-gray-200 rotate-12 opacity-25"></div>
        
        {/* Floating accents */}
        <div className="absolute top-1/3 left-1/5 w-6 h-6 bg-blue-200 rounded-full opacity-40 animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/3 w-4 h-4 bg-purple-200 rounded-full opacity-50"></div>
        <div className="absolute top-2/3 left-1/3 w-8 h-8 bg-gray-200 rounded-full opacity-30"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-20">
          <motion.h2
          ref={titleRef}
          className="text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-gray-900 mb-6"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 1, ease: "easeOut" }}
        >
            {title}
          </motion.h2>
          
          <motion.p
            ref={subtitleRef}
            className="text-2xl md:text-3xl lg:text-4xl text-gray-600 max-w-4xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
          >
            {subtitle}
          </motion.p>
        </div>

        {/* Projects Grid */}
        <div ref={projectsRef} className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              className="group cursor-pointer"
              initial={{ opacity: 0, y: 100, scale: 0.9 }}
              animate={isInView ? { opacity: 1, y: 0, scale: 1 } : { opacity: 0, y: 100, scale: 0.9 }}
              transition={{ duration: 1.2, delay: index * 0.15, ease: "easeOut" }}
              onMouseEnter={() => setHoveredProject(project.id)}
              onMouseLeave={() => setHoveredProject(null)}
            >
              {/* Project Image */}
              <div className="relative overflow-hidden rounded-2xl mb-6 aspect-[4/3] bg-gray-200">
                {project.image && (
                  <motion.img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-full object-cover"
                    initial={{ scale: 1 }}
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.6, ease: "easeOut" }}
                  />
                )}
                
                {/* Overlay */}
                <motion.div
                  className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-500 flex items-center justify-center"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                >
                  <motion.div
                    className="text-white font-semibold text-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    initial={{ y: 20 }}
                    whileHover={{ y: 0 }}
                  >
                    View Project
                  </motion.div>
                </motion.div>

                {/* Year Badge */}
                <div className="absolute top-4 right-4 bg-white bg-opacity-90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-800">
                  {project.year}
                </div>
              </div>

              {/* Project Info */}
              <div className="space-y-4">
                {/* Category */}
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-500 uppercase tracking-wider">
                    {project.category}
                  </span>
                  <div className="h-px bg-gray-300 flex-1"></div>
                </div>

                {/* Title */}
                <h3 className="text-2xl md:text-3xl font-bold text-gray-900 group-hover:text-gray-700 transition-colors duration-300">
                  {project.title}
                </h3>

                {/* Description */}
                <p className="text-gray-600 text-lg leading-relaxed">
                  {project.description}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-2">
                  {project.tags.map((tag, tagIndex) => (
                    <motion.span
                      key={tagIndex}
                      className="px-3 py-1 bg-gray-100 text-gray-700 text-sm font-medium rounded-full group-hover:bg-gray-200 transition-colors duration-300"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.5, delay: (index * 0.15) + (tagIndex * 0.1) + 0.5 }}
                    >
                      {tag}
                    </motion.span>
                  ))}
                </div>

                {/* View Project Link */}
                <motion.div
                  className="flex items-center text-gray-400 group-hover:text-gray-600 transition-colors duration-300 pt-2"
                  animate={{
                    x: hoveredProject === project.id ? 10 : 0
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <span className="text-sm font-medium tracking-wider uppercase">View Project</span>
                  <svg
                    className="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 8l4 4m0 0l-4 4m4-4H3"
                    />
                  </svg>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-20"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 1, delay: 1, ease: "easeOut" }}
        >
          <p className="text-lg text-gray-600 mb-8">
            Want to see more of our work?
          </p>
          <motion.button
            className="inline-flex items-center px-8 py-4 bg-gray-900 text-white font-semibold text-lg rounded-full hover:bg-gray-800 transition-all duration-300 group"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            View All Projects
            <svg
              className="ml-3 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 8l4 4m0 0l-4 4m4-4H3"
              />
            </svg>
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default InspiredWork;