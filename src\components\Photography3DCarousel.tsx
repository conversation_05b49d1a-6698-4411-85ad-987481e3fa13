'use client'

import React, { useState } from 'react'
import { motion, PanInfo } from 'framer-motion'
import { ChevronLeft, ChevronRight } from 'lucide-react'

interface CarouselImage {
  id: number
  src: string
  alt: string
  category: string
}

const sampleImages: CarouselImage[] = [
  {
    id: 1,
    src: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=600&fit=crop",
    alt: "Modern Architecture",
    category: "Architecture"
  },
  {
    id: 2,
    src: "https://images.unsplash.com/photo-1565814329452-e1efa11c5b89?w=400&h=600&fit=crop",
    alt: "Interior Design",
    category: "Interior"
  },
  {
    id: 3,
    src: "https://images.unsplash.com/photo-1551632811-561732d1e306?w=400&h=600&fit=crop",
    alt: "Restaurant Photography",
    category: "Food"
  },
  {
    id: 4,
    src: "https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=400&h=600&fit=crop",
    alt: "Business Portrait",
    category: "Portrait"
  },
  {
    id: 5,
    src: "https://images.unsplash.com/photo-1497366216548-37526070297c?w=400&h=600&fit=crop",
    alt: "Office Space",
    category: "Commercial"
  },
  {
    id: 6,
    src: "https://images.unsplash.com/photo-1560472355-536de3962603?w=400&h=600&fit=crop",
    alt: "Luxury Home",
    category: "Real Estate"
  },
  {
    id: 7,
    src: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=600&fit=crop",
    alt: "Product Photography",
    category: "Product"
  },
  {
    id: 8,
    src: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=600&fit=crop",
    alt: "Event Photography",
    category: "Event"
  }
]

export default function Photography3DCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const [rotation, setRotation] = useState(0)

  const totalImages = sampleImages.length
  const radius = 300
  const angleStep = 360 / totalImages

  const nextSlide = () => {
    const newIndex = (currentIndex + 1) % totalImages
    const newRotation = rotation - angleStep
    setCurrentIndex(newIndex)
    setRotation(newRotation)
  }

  const prevSlide = () => {
    const newIndex = (currentIndex - 1 + totalImages) % totalImages
    const newRotation = rotation + angleStep
    setCurrentIndex(newIndex)
    setRotation(newRotation)
  }

  const goToSlide = (index: number) => {
    const diff = index - currentIndex
    const newRotation = rotation - (diff * angleStep)
    setCurrentIndex(index)
    setRotation(newRotation)
  }

  // Handle drag/swipe
  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    setIsDragging(false)

    const swipeThreshold = 50
    const swipeVelocityThreshold = 500

    // Check if it's a significant swipe
    if (Math.abs(info.offset.x) > swipeThreshold || Math.abs(info.velocity.x) > swipeVelocityThreshold) {
      if (info.offset.x > 0 || info.velocity.x > 0) {
        // Swiped right - go to previous
        prevSlide()
      } else {
        // Swiped left - go to next
        nextSlide()
      }
    }
  }

  return (
    <section className="py-24 md:py-32 bg-gray-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-64 h-64 bg-white rounded-full opacity-40" />
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-gray-900 opacity-5 rotate-45" />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-20">
          <p className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6">
            Our Work
          </p>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Photography Portfolio
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-12">
            Explore our photography work through this interactive 3D carousel. Navigate through our diverse portfolio.
          </p>
        </div>

        {/* 3D Carousel Container */}
        <div className="relative h-[500px] flex items-center justify-center">
          <motion.div
            className="relative w-full h-full cursor-grab active:cursor-grabbing"
            style={{
              perspective: '1000px',
              transformStyle: 'preserve-3d'
            }}
            drag="x"
            dragConstraints={{ left: 0, right: 0 }}
            dragElastic={0}
            dragMomentum={false}
            onDragStart={() => setIsDragging(true)}
            onDragEnd={handleDragEnd}
            whileDrag={{ cursor: 'grabbing' }}
            animate={{ x: 0 }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            {/* Carousel Items */}
            {sampleImages.map((image, index) => {
              const baseAngle = index * angleStep
              const totalAngle = baseAngle + rotation
              const isCenter = index === currentIndex
              const distance = Math.abs(index - currentIndex)
              const isVisible = distance <= 3 // Show 3 items on each side

              if (!isVisible) return null

              return (
                <motion.div
                  key={image.id}
                  className="absolute left-1/2 top-1/2 cursor-pointer"
                  style={{
                    transformOrigin: 'center center',
                  }}
                  animate={{
                    transform: `
                      translate(-50%, -50%)
                      rotateY(${totalAngle}deg)
                      translateZ(${radius}px)
                    `,
                    scale: isCenter ? 1.1 : Math.max(0.7, 1 - distance * 0.1),
                    opacity: isCenter ? 1 : Math.max(0.4, 1 - distance * 0.15)
                  }}
                  transition={{
                    duration: 0.8,
                    ease: [0.25, 0.46, 0.45, 0.94],
                    type: "spring",
                    stiffness: 100,
                    damping: 20
                  }}
                  onClick={() => !isCenter && !isDragging && goToSlide(index)}
                  whileHover={!isCenter ? { scale: isCenter ? 1.1 : Math.max(0.75, 1 - distance * 0.1) } : {}}
                >
                  <div className="relative group">
                    {/* Image Card */}
                    <div className="w-48 h-64 md:w-56 md:h-80 rounded-2xl overflow-hidden shadow-2xl bg-white p-2">
                      <img
                        src={image.src}
                        alt={image.alt}
                        className="w-full h-full object-cover rounded-xl"
                        draggable={false}
                      />

                      {/* Category Badge */}
                      <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1">
                        <p className="text-xs font-semibold text-gray-900">{image.category}</p>
                      </div>
                    </div>

                    {/* Center Highlight */}
                    {isCenter && (
                      <motion.div
                        className="absolute inset-0 rounded-2xl border-2 border-blue-500 shadow-lg shadow-blue-500/25"
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3 }}
                      />
                    )}
                  </div>
                </motion.div>
              )
            })}
          </motion.div>

          {/* Navigation Arrows */}
          <button
            onClick={prevSlide}
            className="absolute left-8 top-1/2 -translate-y-1/2 z-20 p-4 bg-white/95 backdrop-blur-sm rounded-full shadow-xl hover:bg-white hover:scale-110 transition-all duration-200 border border-gray-200"
          >
            <ChevronLeft className="w-6 h-6 text-gray-700" />
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-8 top-1/2 -translate-y-1/2 z-20 p-4 bg-white/95 backdrop-blur-sm rounded-full shadow-xl hover:bg-white hover:scale-110 transition-all duration-200 border border-gray-200"
          >
            <ChevronRight className="w-6 h-6 text-gray-700" />
          </button>
        </div>

        {/* Navigation Dots */}
        <div className="flex justify-center mt-12 space-x-3">
          {sampleImages.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-gray-900 scale-125'
                  : 'bg-gray-300 hover:bg-gray-500'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>

        {/* Instructions */}
        <div className="text-center mt-8">
          <p className="text-gray-500 text-sm">
            Drag to swipe • Click arrows to navigate • Click dots to jump • Click images to select
          </p>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <a
            href="/portfolio"
            className="inline-flex items-center px-8 py-4 bg-gray-900 text-white font-bold text-sm tracking-wide uppercase hover:bg-gray-800 transition-colors duration-300 rounded-lg"
          >
            View Full Portfolio
            <svg className="ml-2 w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </a>
        </div>
      </div>
    </section>
  )
}
