"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { Mail, Phone, MapPin, Linkedin, Instagram, Facebook } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useSmoothScroll } from "@/hooks/useSmoothScroll";

const footerLinks = {
  services: [
    { title: "Content Creation", href: "/services/content-creation" },
    { title: "Photography", href: "/services/photography" },
    { title: "Web Development", href: "/services/web-development" },
  ],
  company: [
    { title: "About Us", href: "/about" },
    { title: "Portfolio", href: "/portfolio" },
    { title: "Contact", href: "/contact" },
  ],
  legal: [
    { title: "Privacy Policy", href: "/privacy" },
    { title: "Terms of Service", href: "/terms" },
    { title: "Cookie Policy", href: "/cookies" },
  ],
};

const socialLinks = [
  {
    name: "LinkedIn",
    href: "https://www.linkedin.com/company/winova-lu",
    icon: Linkedin,
  },
  {
    name: "Instagram",
    href: "https://www.instagram.com/winova.lu",
    icon: Instagram,
  },
  {
    name: "Facebook",
    href: "https://www.facebook.com/winova.lu",
    icon: Facebook,
  },
];

const contactInfo = [
  {
    icon: Mail,
    label: "Email",
    value: "<EMAIL>",
    href: "mailto:<EMAIL>",
  },
  {
    icon: Phone,
    label: "Phone",
    value: "+352 XX XX XX XX",
    href: "tel:+352XXXXXXXX",
  },
  {
    icon: MapPin,
    label: "Location",
    value: "Luxembourg City, Luxembourg",
    href: "https://maps.google.com/?q=Luxembourg+City",
  },
];

export function Footer() {
  const currentYear = new Date().getFullYear();
  const { scrollToTarget, scrollToTop } = useSmoothScroll();

  return (
    <footer className="bg-secondary text-secondary-foreground">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-16 lg:py-20">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
            {/* Brand Section */}
            <motion.div
              className="lg:col-span-1"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <div 
                className="flex items-center space-x-2 mb-6 cursor-pointer"
                onClick={scrollToTop}
              >
                <div className="w-8 h-8 bg-gradient-to-br from-accent to-blue-400 rounded-lg flex items-center justify-center">
                  <span className="text-primary font-bold text-lg">W</span>
                </div>
                <span className="text-xl font-bold text-accent">Winova</span>
              </div>
              <p className="text-muted-foreground mb-6 max-w-sm">
                Premium creative agency in Luxembourg specializing in content creation, 
                professional photography, and modern web development.
              </p>
              <div className="flex space-x-4">
                {socialLinks.map((social) => (
                  <motion.a
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 bg-accent/10 hover:bg-accent/20 rounded-lg flex items-center justify-center transition-colors group"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <social.icon className="w-5 h-5 text-accent group-hover:text-accent/80 transition-colors" />
                  </motion.a>
                ))}
              </div>
            </motion.div>

            {/* Services */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <h3 className="text-lg font-semibold text-accent mb-6">Services</h3>
              <ul className="space-y-3">
                {footerLinks.services.map((link) => (
                  <li key={link.title}>
                    <button
                      onClick={() => scrollToTarget(link.href)}
                      className="text-muted-foreground hover:text-accent transition-colors duration-200 text-left"
                    >
                      {link.title}
                    </button>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Company */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h3 className="text-lg font-semibold text-accent mb-6">Company</h3>
              <ul className="space-y-3">
                {footerLinks.company.map((link) => (
                  <li key={link.title}>
                    <button
                      onClick={() => scrollToTarget(link.href)}
                      className="text-muted-foreground hover:text-accent transition-colors duration-200 text-left"
                    >
                      {link.title}
                    </button>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Contact Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <h3 className="text-lg font-semibold text-accent mb-6">Contact</h3>
              <ul className="space-y-4">
                {contactInfo.map((contact) => (
                  <li key={contact.label}>
                    <a
                      href={contact.href}
                      className="flex items-center space-x-3 text-muted-foreground hover:text-accent transition-colors duration-200 group"
                    >
                      <contact.icon className="w-4 h-4 text-accent group-hover:scale-110 transition-transform" />
                      <span className="text-sm">{contact.value}</span>
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>
        </div>

        {/* Newsletter Section */}
        <motion.div
          className="border-t border-accent/20 py-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="max-w-2xl mx-auto text-center">
            <h3 className="text-xl font-semibold text-accent mb-2">
              Stay Updated
            </h3>
            <p className="text-muted-foreground mb-6">
              Get the latest insights on creative trends and digital innovation.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-2 bg-accent/10 border border-accent/20 rounded-lg text-accent placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-accent/50 focus:border-transparent"
              />
              <Button className="bg-accent hover:bg-accent/90 text-primary px-6">
                Subscribe
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Bottom Bar */}
        <div className="border-t border-accent/20 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-muted-foreground">
              © {currentYear} Winova. All rights reserved.
            </div>
            <div className="flex space-x-6">
              {footerLinks.legal.map((link) => (
                <Link
                  key={link.title}
                  href={link.href}
                  className="text-sm text-muted-foreground hover:text-accent transition-colors duration-200"
                >
                  {link.title}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}