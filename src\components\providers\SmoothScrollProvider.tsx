'use client'

import { ReactLenis } from 'lenis/react'
import { ReactNode, useEffect } from 'react'

interface SmoothScrollProviderProps {
  children: ReactNode
}

export function SmoothScrollProvider({ children }: SmoothScrollProviderProps) {
  useEffect(() => {
    // Disable smooth scrolling for wheel events to prevent lag
    const handleWheel = (e: WheelEvent) => {
      // Allow native wheel scrolling for better performance
      e.stopPropagation()
    }

    // Add wheel event listener with high priority
    document.addEventListener('wheel', handleWheel, { passive: false, capture: true })

    return () => {
      document.removeEventListener('wheel', handleWheel, { capture: true })
    }
  }, [])

  return (
    <ReactLenis
      root
      options={{
        // Minimal smooth scrolling - only for programmatic scrolling
        lerp: 0.15,
        duration: 0.8,
        easing: (t) => t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2,
        direction: 'vertical',
        gestureDirection: 'vertical',
        smooth: false, // Disable smooth scrolling entirely
        smoothTouch: false,
        touchMultiplier: 1,
        infinite: false,
        autoResize: true,
        syncTouch: false,
        wheelMultiplier: 0, // Disable wheel handling by Lenis
        normalizeWheel: false,
      }}
    >
      {children}
    </ReactLenis>
  )
}