'use client'

import React, { useRef, useState, useEffect } from 'react'
import { motion, useInView, useScroll, useTransform, AnimatePresence } from 'framer-motion'
import { Canvas, useFrame, useThree } from '@react-three/fiber'
import { Float, Text3D, MeshDistortMaterial, Sphere, Box, Environment, Stars } from '@react-three/drei'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import * as THREE from 'three'

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger)
}

interface ContactMethod {
  id: string
  title: string
  description: string
  icon: string
  value: string
  color: string
  gradient: string
}

const contactMethods: ContactMethod[] = [
  {
    id: 'email',
    title: 'Email',
    description: 'Drop us a line anytime',
    icon: '📧',
    value: '<EMAIL>',
    color: '#9ca3af',
    gradient: 'from-gray-100 to-gray-200'
  },
  {
    id: 'phone',
    title: 'Phone',
    description: 'Call us for immediate support',
    icon: '📞',
    value: '+****************',
    color: '#9ca3af',
    gradient: 'from-gray-100 to-gray-200'
  },
  {
    id: 'location',
    title: 'Location',
    description: 'Visit our creative studio',
    icon: '📍',
    value: 'San Francisco, CA',
    color: '#9ca3af',
    gradient: 'from-gray-100 to-gray-200'
  },
  {
    id: 'social',
    title: 'Social',
    description: 'Follow our journey',
    icon: '🌐',
    value: '@winova_studio',
    color: '#9ca3af',
    gradient: 'from-gray-100 to-gray-200'
  }
]

// 3D Contact Visualization
function ContactSphere({ isActive }: { isActive: boolean }) {
  const meshRef = useRef<THREE.Mesh>(null)
  const { viewport } = useThree()
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.3) * 0.2
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.2
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })
  
  return (
    <Float speed={2} rotationIntensity={0.5} floatIntensity={1}>
      <Sphere ref={meshRef} args={[1, 64, 64]} position={[0, 0, 0]}>
        <MeshDistortMaterial
          color={isActive ? '#9ca3af' : '#d1d5db'}
          attach="material"
          distort={0.1}
          speed={1}
          roughness={0.3}
          metalness={0.2}
          emissive={isActive ? '#9ca3af' : '#d1d5db'}
          emissiveIntensity={isActive ? 0.1 : 0.05}
        />
      </Sphere>
    </Float>
  )
}

// Floating Particles
function FloatingParticles() {
  const particlesRef = useRef<THREE.Group>(null)
  
  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.05
    }
  })
  
  const particles = Array.from({ length: 50 }, (_, i) => {
    const x = (Math.random() - 0.5) * 20
    const y = (Math.random() - 0.5) * 20
    const z = (Math.random() - 0.5) * 20
    
    return (
      <Float key={i} speed={Math.random() * 2 + 1} rotationIntensity={0.5}>
        <Sphere args={[0.02, 8, 8]} position={[x, y, z]}>
          <meshStandardMaterial 
            color={`hsl(${Math.random() * 360}, 70%, 60%)`}
            emissive={`hsl(${Math.random() * 360}, 70%, 30%)`}
            emissiveIntensity={0.2}
          />
        </Sphere>
      </Float>
    )
  })
  
  return <group ref={particlesRef}>{particles}</group>
}

// Contact Method Card
function ContactCard({ method, index }: { method: ContactMethod; index: number }) {
  const cardRef = useRef<HTMLDivElement>(null)
  const [isHovered, setIsHovered] = useState(false)
  
  useEffect(() => {
    if (!cardRef.current) return
    
    const card = cardRef.current
    
    const handleMouseMove = (e: MouseEvent) => {
      const rect = card.getBoundingClientRect()
      const x = (e.clientX - rect.left) / rect.width - 0.5
      const y = (e.clientY - rect.top) / rect.height - 0.5
      
      gsap.to(card, {
        rotationX: y * 10,
        rotationY: x * 10,
        duration: 0.3,
        ease: 'power2.out',
        transformPerspective: 1000
      })
    }
    
    const handleMouseLeave = () => {
      gsap.to(card, {
        rotationX: 0,
        rotationY: 0,
        duration: 0.5,
        ease: 'elastic.out(1, 0.3)'
      })
    }
    
    card.addEventListener('mousemove', handleMouseMove)
    card.addEventListener('mouseleave', handleMouseLeave)
    
    return () => {
      card.removeEventListener('mousemove', handleMouseMove)
      card.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [])
  
  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      className="group cursor-pointer"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className={`relative p-6 bg-white rounded-2xl border border-gray-200 shadow-lg transition-all duration-500 hover:shadow-xl hover:border-gray-300`}>
        {/* Animated Background */}
        <motion.div 
          className="absolute inset-0 rounded-2xl opacity-20"
          animate={{ 
            background: isHovered 
              ? 'radial-gradient(circle at center, rgba(255,255,255,0.3) 0%, transparent 70%)'
              : 'radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)'
          }}
          transition={{ duration: 0.3 }}
        />
        
        <div className="relative z-10">
          <motion.div 
            className="text-4xl mb-4"
            animate={{ 
              scale: isHovered ? 1.2 : 1,
              rotate: isHovered ? 10 : 0
            }}
            transition={{ duration: 0.3 }}
          >
            {method.icon}
          </motion.div>
          
          <h3 className="text-xl font-bold text-gray-900 mb-2">
            {method.title}
          </h3>
          
          <p className="text-gray-600 text-sm mb-3">
            {method.description}
          </p>
          
          <p className="text-gray-900 font-medium">
            {method.value}
          </p>
          
          {/* Hover Effect */}
          <motion.div
            className="absolute bottom-4 right-4 text-gray-400 group-hover:text-gray-900 transition-colors"
            animate={{ x: isHovered ? 5 : 0 }}
            transition={{ duration: 0.2 }}
          >
            →
          </motion.div>
        </div>
      </div>
    </motion.div>
  )
}

// Contact Form
function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: '',
    budget: '',
    timeline: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    setIsSubmitted(true)
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false)
      setFormData({
        name: '',
        email: '',
        company: '',
        message: '',
        budget: '',
        timeline: ''
      })
    }, 3000)
  }
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }
  
  return (
    <motion.form
      onSubmit={handleSubmit}
      className="space-y-6"
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.8, delay: 0.4 }}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-gray-700 text-sm font-medium mb-2">
            Name *
          </label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
            className="w-full px-4 py-3 bg-white border border-gray-300 rounded-xl text-gray-900 placeholder-gray-400 focus:outline-none focus:border-gray-500 focus:bg-gray-50 transition-all duration-300"
            placeholder="Your full name"
          />
        </div>
        
        <div>
          <label className="block text-gray-700 text-sm font-medium mb-2">
            Email *
          </label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            required
            className="w-full px-4 py-3 bg-white border border-gray-300 rounded-xl text-gray-900 placeholder-gray-400 focus:outline-none focus:border-gray-500 focus:bg-gray-50 transition-all duration-300"
            placeholder="<EMAIL>"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-gray-700 text-sm font-medium mb-2">
            Company
          </label>
          <input
            type="text"
            name="company"
            value={formData.company}
            onChange={handleChange}
            className="w-full px-4 py-3 bg-white border border-gray-300 rounded-xl text-gray-900 placeholder-gray-400 focus:outline-none focus:border-gray-500 focus:bg-gray-50 transition-all duration-300"
            placeholder="Your company name"
          />
        </div>
        
        <div>
          <label className="block text-gray-700 text-sm font-medium mb-2">
            Budget Range
          </label>
          <select
            name="budget"
            value={formData.budget}
            onChange={handleChange}
            className="w-full px-4 py-3 bg-white border border-gray-300 rounded-xl text-gray-900 focus:outline-none focus:border-gray-500 focus:bg-gray-50 transition-all duration-300"
          >
            <option value="" className="bg-white">Select budget range</option>
            <option value="10k-25k" className="bg-white">$10k - $25k</option>
            <option value="25k-50k" className="bg-white">$25k - $50k</option>
            <option value="50k-100k" className="bg-white">$50k - $100k</option>
            <option value="100k+" className="bg-white">$100k+</option>
          </select>
        </div>
      </div>
      
      <div>
        <label className="block text-gray-700 text-sm font-medium mb-2">
          Timeline
        </label>
        <select
          name="timeline"
          value={formData.timeline}
          onChange={handleChange}
          className="w-full px-4 py-3 bg-white border border-gray-300 rounded-xl text-gray-900 focus:outline-none focus:border-gray-500 focus:bg-gray-50 transition-all duration-300"
        >
          <option value="" className="bg-white">Select timeline</option>
          <option value="asap" className="bg-white">ASAP</option>
          <option value="1-3months" className="bg-white">1-3 months</option>
          <option value="3-6months" className="bg-white">3-6 months</option>
          <option value="6months+" className="bg-white">6+ months</option>
        </select>
      </div>
      
      <div>
        <label className="block text-gray-700 text-sm font-medium mb-2">
          Message *
        </label>
        <textarea
          name="message"
          value={formData.message}
          onChange={handleChange}
          required
          rows={6}
          className="w-full px-4 py-3 bg-white border border-gray-300 rounded-xl text-gray-900 placeholder-gray-400 focus:outline-none focus:border-gray-500 focus:bg-gray-50 transition-all duration-300 resize-none"
          placeholder="Tell us about your project..."
        />
      </div>
      
      <motion.button
        type="submit"
        disabled={isSubmitting || isSubmitted}
        className="w-full bg-gray-900 text-white font-semibold py-4 rounded-xl hover:bg-gray-800 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        whileHover={{ scale: isSubmitting || isSubmitted ? 1 : 1.02 }}
        whileTap={{ scale: isSubmitting || isSubmitted ? 1 : 0.98 }}
      >
        <AnimatePresence mode="wait">
          {isSubmitting ? (
            <motion.span
              key="submitting"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex items-center justify-center"
            >
              <motion.div
                className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full mr-3"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              />
              Sending...
            </motion.span>
          ) : isSubmitted ? (
            <motion.span
              key="submitted"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="flex items-center justify-center"
            >
              ✓ Message Sent!
            </motion.span>
          ) : (
            <motion.span
              key="default"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              Send Message
            </motion.span>
          )}
        </AnimatePresence>
      </motion.button>
    </motion.form>
  )
}

// Main Advanced Contact Component
export default function AdvancedContact() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [activeMethod, setActiveMethod] = useState<string>('')
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start end', 'end start']
  })
  
  const y = useTransform(scrollYProgress, [0, 1], ['0%', '-10%'])
  const isInView = useInView(containerRef, { once: true, margin: '-100px' })
  
  return (
    <section 
      ref={containerRef}
      className="relative py-32 bg-white overflow-hidden"
    >
      {/* 3D Background */}
      <div className="absolute inset-0 opacity-30">
        <Canvas
          camera={{ position: [0, 0, 10], fov: 45 }}
          gl={{ alpha: true, antialias: true }}
        >
          <ambientLight intensity={0.3} />
          <pointLight position={[10, 10, 10]} intensity={0.5} />
          <ContactSphere isActive={activeMethod !== ''} />
          <FloatingParticles />
          <Stars radius={100} depth={50} count={1000} factor={4} saturation={0} fade speed={1} />
          <Environment preset="night" />
        </Canvas>
      </div>
      
      {/* Clean Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gray-50/30" />
      </div>
      
      <motion.div 
        className="relative z-10 max-w-7xl mx-auto px-6"
        style={{ y }}
      >
        {/* Section Header */}
        <div className="text-center mb-12 sm:mb-16">
          <motion.h2 
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 0.8 }}
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 mb-4 sm:mb-6 px-4"
          >
            Let's Connect
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed px-4"
          >
            Ready to bring your vision to life? Let's discuss how we can create something extraordinary together.
          </motion.p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-start">
          {/* Contact Methods */}
          <div className="space-y-6 sm:space-y-8">
            <motion.h3 
              initial={{ opacity: 0, x: -50 }}
              animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-2xl sm:text-3xl font-bold text-gray-900 mb-6 sm:mb-8"
            >
              Get in Touch
            </motion.h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
              {contactMethods.map((method, index) => (
                <div
                  key={method.id}
                  onMouseEnter={() => setActiveMethod(method.id)}
                  onMouseLeave={() => setActiveMethod('')}
                >
                  <ContactCard method={method} index={index} />
                </div>
              ))}
            </div>
            
            {/* Additional Info */}
            <motion.div 
              initial={{ opacity: 0, x: -50 }}
              animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="bg-gray-50 border border-gray-200 rounded-2xl p-6 mt-8"
            >
              <h4 className="text-xl font-bold text-gray-900 mb-4">Why Choose Winova?</h4>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-center">
                  <span className="text-gray-600 mr-3">✓</span>
                  Cutting-edge technology and innovative solutions
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">✓</span>
                  Expert team with 10+ years of experience
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">✓</span>
                  24/7 support and maintenance
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">✓</span>
                  100% satisfaction guarantee
                </li>
              </ul>
            </motion.div>
          </div>
          
          {/* Contact Form */}
          <div className="bg-white border border-gray-200 rounded-2xl p-6 sm:p-8 shadow-lg">
            <motion.h3 
              initial={{ opacity: 0, x: 50 }}
              animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-2xl sm:text-3xl font-bold text-gray-900 mb-6 sm:mb-8"
            >
              Start Your Project
            </motion.h3>
            
            <ContactForm />
          </div>
        </div>
      </motion.div>
    </section>
  )
}