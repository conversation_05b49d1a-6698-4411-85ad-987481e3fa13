'use client'

import React, { useRef, useEffect, useState } from 'react'
import { <PERSON><PERSON>, useFrame, useThree } from '@react-three/fiber'
import { OrbitControls, Sphere, MeshDistortMaterial, Float, Environment, Sparkles } from '@react-three/drei'
import { motion, useScroll, useTransform, useInView } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { useSmoothScroll } from '@/hooks/useSmoothScroll'
import * as THREE from 'three'

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger)
}

// Sophisticated Abstract Geometric Composition
function AbstractGeometry() {
  const groupRef = useRef<THREE.Group>(null)
  const ringRef = useRef<THREE.Mesh>(null)
  const torusRef = useRef<THREE.Mesh>(null)
  const icosahedronRef = useRef<THREE.Mesh>(null)
  const { viewport } = useThree()
  
  useFrame((state) => {
    const time = state.clock.elapsedTime
    
    if (groupRef.current) {
      groupRef.current.rotation.y = time * 0.1
      groupRef.current.rotation.x = Math.sin(time * 0.3) * 0.1
    }
    
    if (ringRef.current) {
      ringRef.current.rotation.x = time * 0.4
      ringRef.current.rotation.z = Math.sin(time * 0.2) * 0.3
    }
    
    if (torusRef.current) {
      torusRef.current.rotation.y = -time * 0.3
      torusRef.current.rotation.x = Math.cos(time * 0.4) * 0.2
    }
    
    if (icosahedronRef.current) {
      icosahedronRef.current.rotation.x = time * 0.2
      icosahedronRef.current.rotation.y = time * 0.15
      icosahedronRef.current.position.y = Math.sin(time * 0.8) * 0.2
    }
  })

  const scale = viewport.width > 4 ? 1.2 : 0.8

  return (
    <Float speed={1.5} rotationIntensity={0.5} floatIntensity={1}>
      <group ref={groupRef} scale={scale}>
        {/* Central Icosahedron */}
        <mesh ref={icosahedronRef} position={[0, 0, 0]}>
          <icosahedronGeometry args={[1, 1]} />
          <meshPhysicalMaterial
            color="#1e293b"
            metalness={0.9}
            roughness={0.1}
            transmission={0.1}
            thickness={0.5}
            clearcoat={1}
            clearcoatRoughness={0}
          />
        </mesh>
        
        {/* Outer Ring */}
        <mesh ref={ringRef} position={[0, 0, 0]}>
          <torusGeometry args={[2.5, 0.1, 16, 100]} />
          <meshPhysicalMaterial
            color="#3b82f6"
            metalness={0.8}
            roughness={0.2}
            transmission={0.3}
            thickness={0.2}
            emissive="#1e40af"
            emissiveIntensity={0.1}
          />
        </mesh>
        
        {/* Inner Torus */}
        <mesh ref={torusRef} position={[0, 0, 0]} rotation={[Math.PI / 2, 0, 0]}>
          <torusGeometry args={[1.8, 0.15, 12, 48]} />
          <meshPhysicalMaterial
            color="#10b981"
            metalness={0.7}
            roughness={0.3}
            transmission={0.2}
            thickness={0.3}
            emissive="#047857"
            emissiveIntensity={0.05}
          />
        </mesh>
        
        {/* Floating Elements */}
        {Array.from({ length: 8 }, (_, i) => {
          const angle = (i / 8) * Math.PI * 2
          const radius = 3.5
          const x = Math.cos(angle) * radius
          const z = Math.sin(angle) * radius
          
          return (
            <mesh
              key={i}
              position={[x, Math.sin(angle * 2) * 0.5, z]}
              rotation={[angle, angle * 0.5, 0]}
            >
              <octahedronGeometry args={[0.15, 0]} />
              <meshPhysicalMaterial
                color={i % 2 === 0 ? "#8b5cf6" : "#f59e0b"}
                metalness={0.6}
                roughness={0.4}
                emissive={i % 2 === 0 ? "#6d28d9" : "#d97706"}
                emissiveIntensity={0.2}
              />
            </mesh>
          )
        })}
      </group>
    </Float>
  )
}

// Enhanced Floating Particles Component
function FloatingParticles() {
  return (
    <>
      <Sparkles
        count={60}
        scale={[6, 6, 6]}
        size={1.5}
        speed={0.3}
        opacity={0.4}
        color="#3b82f6"
      />
      <Sparkles
        count={40}
        scale={[8, 8, 8]}
        size={1}
        speed={0.2}
        opacity={0.3}
        color="#10b981"
      />
      <Sparkles
        count={30}
        scale={[10, 10, 10]}
        size={0.8}
        speed={0.15}
        opacity={0.2}
        color="#8b5cf6"
      />
    </>
  )
}

// Simple 3D Text Component (without external font dependency)
function Hero3DText() {
  const textRef = useRef<THREE.Group>(null)
  
  useFrame((state) => {
    if (textRef.current) {
      textRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  return (
    <group ref={textRef} position={[0, 1, 0]}>
      {/* Using simple geometry instead of Text3D to avoid font loading issues */}
      <mesh>
        <boxGeometry args={[2, 0.3, 0.1]} />
        <meshStandardMaterial color="#6b7280" metalness={0.3} roughness={0.7} />
      </mesh>
    </group>
  )
}

// Main Advanced Hero Component
export default function AdvancedHero() {
  const containerRef = useRef<HTMLDivElement>(null)
  const canvasRef = useRef<HTMLDivElement>(null)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isLoaded, setIsLoaded] = useState(false)
  const { scrollToTarget, scrollToElement } = useSmoothScroll()
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start start', 'end start']
  })
  
  const y = useTransform(scrollYProgress, [0, 1], ['0%', '50%'])
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0])
  const scale = useTransform(scrollYProgress, [0, 0.5], [1, 0.8])
  
  const isInView = useInView(containerRef, { once: true, margin: '-100px' })

  useEffect(() => {
    setIsLoaded(true)
    
    // Advanced GSAP animations
    const tl = gsap.timeline()
    
    tl.from('.hero-title', {
      duration: 1.2,
      y: 100,
      opacity: 0,
      ease: 'power4.out',
      stagger: 0.1
    })
    .from('.hero-subtitle', {
      duration: 1,
      y: 50,
      opacity: 0,
      ease: 'power3.out'
    }, '-=0.8')
    .from('.hero-cta', {
      duration: 0.8,
      y: 30,
      opacity: 0,
      ease: 'back.out(1.7)'
    }, '-=0.5')
    
    // Mouse movement parallax
    const handleMouseMove = (e: MouseEvent) => {
      const { clientX, clientY } = e
      const { innerWidth, innerHeight } = window
      
      setMousePosition({
        x: (clientX / innerWidth - 0.5) * 2,
        y: (clientY / innerHeight - 0.5) * 2
      })
    }
    
    window.addEventListener('mousemove', handleMouseMove)
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
    }
  }, [])

  return (
    <section 
      ref={containerRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-white"
    >
      {/* Clean Background */}
      <div className="absolute inset-0 bg-gray-50/30" />
      
      {/* 3D Canvas */}
      <motion.div 
        ref={canvasRef}
        className="absolute inset-0 z-10"
        style={{ 
          y,
          opacity,
          scale,
          transform: `translate3d(${mousePosition.x * 10}px, ${mousePosition.y * 10}px, 0)`
        }}
      >
        {isLoaded && (
          <Canvas
            camera={{ position: [0, 0, 5], fov: 75 }}
            gl={{ antialias: true, alpha: true }}
            dpr={[1, 2]}
          >
            <ambientLight intensity={0.3} />
            <directionalLight position={[10, 10, 5]} intensity={1} castShadow />
            <pointLight position={[5, 5, 5]} intensity={0.8} color="#3b82f6" />
            <pointLight position={[-5, -5, 5]} intensity={0.6} color="#10b981" />
            <spotLight position={[0, 10, 0]} angle={0.3} penumbra={1} intensity={0.4} color="#8b5cf6" />
            
            <AbstractGeometry />
            <FloatingParticles />
            
            <Environment preset="night" />
            <OrbitControls enableZoom={false} enablePan={false} enableRotate={false} />
          </Canvas>
        )}
      </motion.div>
      
      {/* Content */}
      <div className="relative z-20 text-center px-6 max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="hero-title text-4xl sm:text-5xl md:text-6xl lg:text-8xl xl:text-9xl font-bold text-gray-900 mb-4 sm:mb-6 tracking-tight px-4">
            WINOVA
          </h1>
          
          <p className="hero-subtitle text-lg sm:text-xl md:text-2xl lg:text-3xl text-gray-600 mb-6 sm:mb-8 max-w-4xl mx-auto leading-relaxed px-4">
            Crafting extraordinary digital experiences with cutting-edge technology and innovative design
          </p>
          
          <div className="hero-cta flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center px-4">
            <motion.button
              onClick={() => scrollToTarget('/contact')}
              className="group relative w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 bg-gray-900 text-white font-semibold rounded-lg transition-all duration-300 hover:bg-gray-800 text-sm sm:text-base"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Start Your Project
            </motion.button>
            
            <motion.button
              onClick={() => scrollToTarget('/portfolio')}
              className="group w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 border-2 border-gray-300 text-gray-900 font-semibold rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 text-sm sm:text-base"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              View Our Work
              <span className="inline-block ml-2 transition-transform group-hover:translate-x-1">→</span>
            </motion.button>
          </div>
        </motion.div>
        
        {/* Scroll Indicator */}
        <motion.div 
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          onClick={() => scrollToElement('#services')}
        >
          <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center hover:border-gray-600 transition-colors">
            <div className="w-1 h-3 bg-gray-500 rounded-full mt-2" />
          </div>
        </motion.div>
      </div>
      

    </section>
  )
}