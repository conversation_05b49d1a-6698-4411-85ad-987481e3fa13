'use client'

import React, { useRef, useState, useEffect } from 'react'
import { motion, useInView, useScroll, useTransform, AnimatePresence } from 'framer-motion'
import { Canvas, useFrame, useThree } from '@react-three/fiber'
import { Float, MeshDistortMaterial, Sphere, Box, Image as DreiImage, Environment } from '@react-three/drei'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import * as THREE from 'three'

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger)
}

interface Project {
  id: string
  title: string
  category: string
  description: string
  technologies: string[]
  image: string
  color: string
  gradient: string
  year: string
  client: string
}

const projects: Project[] = [
  {
    id: 'luxury-real-estate',
    title: 'Luxury Real Estate Platform',
    category: 'Web Development',
    description: 'An immersive 3D real estate platform featuring virtual tours, AI-powered property matching, and advanced visualization tools.',
    technologies: ['Next.js', 'Three.js', 'WebXR', 'AI/ML', 'WebGL'],
    image: '/api/placeholder/600/400',
    color: '#6b7280',
    gradient: 'from-gray-100 to-gray-200',
    year: '2024',
    client: 'LuxuryHomes'
  },
  {
    id: 'restaurant-experience',
    title: 'Interactive Restaurant Experience',
    category: 'Digital Experience',
    description: 'A revolutionary dining experience combining AR menus, interactive table surfaces, and real-time kitchen visualization.',
    technologies: ['React', 'WebAR', 'IoT', 'Real-time Data', 'Motion Graphics'],
    image: '/api/placeholder/600/400',
    color: '#6b7280',
    gradient: 'from-gray-100 to-gray-200',
    year: '2024',
    client: 'Gastronomy+'
  },
  {
    id: 'ai-creative-studio',
    title: 'AI Creative Studio',
    category: 'Creative Technology',
    description: 'An AI-powered creative platform that generates, edits, and optimizes visual content using cutting-edge machine learning.',
    technologies: ['Python', 'TensorFlow', 'WebGL', 'Cloud Computing', 'API Integration'],
    image: '/api/placeholder/600/400',
    color: '#6b7280',
    gradient: 'from-gray-100 to-gray-200',
    year: '2024',
    client: 'CreativeAI'
  },
  {
    id: 'sustainable-fashion',
    title: 'Sustainable Fashion Marketplace',
    category: 'E-commerce',
    description: 'A blockchain-powered marketplace for sustainable fashion with virtual try-on and supply chain transparency.',
    technologies: ['Blockchain', 'WebRTC', 'Computer Vision', 'Smart Contracts', 'PWA'],
    image: '/api/placeholder/600/400',
    color: '#6b7280',
    gradient: 'from-gray-100 to-gray-200',
    year: '2024',
    client: 'EcoFashion'
  },
  {
    id: 'metaverse-workspace',
    title: 'Metaverse Workspace',
    category: 'Virtual Reality',
    description: 'A collaborative virtual workspace enabling teams to work together in immersive 3D environments.',
    technologies: ['WebXR', 'WebRTC', 'Spatial Audio', 'Cloud Rendering', 'Real-time Collaboration'],
    image: '/api/placeholder/600/400',
    color: '#6b7280',
    gradient: 'from-gray-100 to-gray-200',
    year: '2024',
    client: 'MetaWork'
  },
  {
    id: 'neural-art-generator',
    title: 'Neural Art Generator',
    category: 'AI Art',
    description: 'An advanced neural network system that creates unique digital artworks based on emotional and contextual inputs.',
    technologies: ['PyTorch', 'GANs', 'WebGL Shaders', 'Real-time Processing', 'Cloud GPU'],
    image: '/api/placeholder/600/400',
    color: '#6b7280',
    gradient: 'from-gray-100 to-gray-200',
    year: '2024',
    client: 'ArtificialMuse'
  }
]

// 3D Project Visualization
function Project3D({ project, isActive }: { project: Project; isActive: boolean }) {
  const meshRef = useRef<THREE.Group>(null)
  const { viewport } = useThree()
  
  useFrame((state) => {
    if (meshRef.current && isActive) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.8) * 0.05
    }
  })
  
  return (
    <group ref={meshRef}>
      <Float speed={2} rotationIntensity={0.5} floatIntensity={1}>
        <Box args={[2, 1.2, 0.1]} position={[0, 0, 0]}>
          <meshStandardMaterial 
            color="#f3f4f6" 
            metalness={0.3} 
            roughness={0.7}
            emissive="#e5e7eb"
            emissiveIntensity={isActive ? 0.1 : 0.02}
          />
        </Box>
        
        {/* Project Title in 3D - Simple Box */}
        <mesh position={[0, 0.8, 0.1]}>
          <boxGeometry args={[1, 0.2, 0.1]} />
          <meshStandardMaterial color="#9ca3af" />
        </mesh>
      </Float>
    </group>
  )
}

// Project Card Component
function ProjectCard({ project, index, isActive, onClick }: { 
  project: Project; 
  index: number; 
  isActive: boolean;
  onClick: () => void;
}) {
  const cardRef = useRef<HTMLDivElement>(null)
  const [isHovered, setIsHovered] = useState(false)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  
  useEffect(() => {
    if (!cardRef.current) return
    
    const card = cardRef.current
    
    const handleMouseMove = (e: MouseEvent) => {
      const rect = card.getBoundingClientRect()
      const x = (e.clientX - rect.left) / rect.width - 0.5
      const y = (e.clientY - rect.top) / rect.height - 0.5
      
      setMousePosition({ x: x * 20, y: y * 20 })
      
      gsap.to(card, {
        rotationX: y * 5,
        rotationY: x * 5,
        duration: 0.3,
        ease: 'power2.out',
        transformPerspective: 1000
      })
    }
    
    const handleMouseLeave = () => {
      setMousePosition({ x: 0, y: 0 })
      gsap.to(card, {
        rotationX: 0,
        rotationY: 0,
        duration: 0.5,
        ease: 'elastic.out(1, 0.3)'
      })
    }
    
    card.addEventListener('mousemove', handleMouseMove)
    card.addEventListener('mouseleave', handleMouseLeave)
    
    return () => {
      card.removeEventListener('mousemove', handleMouseMove)
      card.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [])
  
  return (
    <motion.div
      ref={cardRef}
      className={`group relative cursor-pointer transition-all duration-500 ${
        isActive ? 'scale-105 z-10' : 'scale-100'
      }`}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      layout
    >
      {/* 3D Canvas Background */}
      <div className="absolute inset-0 rounded-2xl overflow-hidden">
        <Canvas
          camera={{ position: [0, 0, 5], fov: 45 }}
          gl={{ alpha: true, antialias: true }}
        >
          <ambientLight intensity={0.5} />
          <pointLight position={[10, 10, 10]} intensity={1} />
          <Project3D project={project} isActive={isActive || isHovered} />
          <Environment preset="night" />
        </Canvas>
      </div>
      
      {/* Card Content */}
      <div className={`relative h-80 sm:h-96 bg-white rounded-2xl p-4 sm:p-6 border border-gray-200 shadow-lg overflow-hidden transition-all duration-500 ${
        isActive ? 'shadow-xl border-gray-300' : ''
      }`}>
        {/* Clean Background Pattern */}
        <motion.div 
          className="absolute inset-0 opacity-10"
          animate={{ 
            background: isActive 
              ? `radial-gradient(circle at ${mousePosition.x + 50}% ${mousePosition.y + 50}%, rgba(156,163,175,0.2) 0%, transparent 50%)`
              : 'radial-gradient(circle at 50% 50%, rgba(156,163,175,0.1) 0%, transparent 50%)'
          }}
          transition={{ duration: 0.3 }}
        />
        
        {/* Content */}
        <div className="relative z-10 h-full flex flex-col">
          <div className="flex justify-between items-start mb-4">
            <span className="text-gray-600 text-sm font-medium">
              {project.category}
            </span>
            <span className="text-gray-600 text-sm">
              {project.year}
            </span>
          </div>
          
          <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-2 sm:mb-3 line-clamp-2">
            {project.title}
          </h3>
          
          <p className="text-gray-700 text-xs sm:text-sm leading-relaxed mb-3 sm:mb-4 flex-1 line-clamp-3">
            {project.description}
          </p>
          
          <div className="mb-4">
            <div className="flex flex-wrap gap-1 sm:gap-2">
              {project.technologies.slice(0, 3).map((tech) => (
                <span 
                  key={tech}
                  className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full"
                >
                  {tech}
                </span>
              ))}
              {project.technologies.length > 3 && (
                <span className="text-xs text-gray-500">
                  +{project.technologies.length - 3} more
                </span>
              )}
            </div>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-600 text-xs sm:text-sm">
              Client: {project.client}
            </span>
            <motion.div
              className="text-gray-600 group-hover:text-gray-900 transition-colors"
              animate={{ x: isHovered ? 5 : 0 }}
              transition={{ duration: 0.2 }}
            >
              →
            </motion.div>
          </div>
        </div>
        
        {/* Active Indicator */}
        <AnimatePresence>
          {isActive && (
            <motion.div
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              className="absolute top-4 right-4 w-3 h-3 bg-gray-600 rounded-full shadow-lg"
            />
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  )
}

// Main Advanced Portfolio Component
export default function AdvancedPortfolio() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [activeProject, setActiveProject] = useState<string>(projects[0].id)
  const [filter, setFilter] = useState<string>('All')
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start end', 'end start']
  })
  
  const y = useTransform(scrollYProgress, [0, 1], ['0%', '-10%'])
  const isInView = useInView(containerRef, { once: true, margin: '-100px' })
  
  const categories = ['All', ...Array.from(new Set(projects.map(p => p.category)))]
  
  const filteredProjects = filter === 'All' 
    ? projects 
    : projects.filter(p => p.category === filter)
  
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveProject(prev => {
        const currentIndex = projects.findIndex(p => p.id === prev)
        const nextIndex = (currentIndex + 1) % projects.length
        return projects[nextIndex].id
      })
    }, 5000)
    
    return () => clearInterval(interval)
  }, [])
  
  return (
    <section 
      ref={containerRef}
      className="relative py-16 sm:py-24 lg:py-32 bg-gray-50 overflow-hidden"
    >
      {/* Clean Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-white/50" />
      </div>
      
      <motion.div 
        className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6"
        style={{ y }}
      >
        {/* Section Header */}
        <div className="text-center mb-12 sm:mb-16">
          <motion.h2 
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 0.8 }}
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 mb-4 sm:mb-6"
          >
            Featured Work
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed px-4"
          >
            Pushing the boundaries of what's possible with cutting-edge technology and innovative design
          </motion.p>
        </div>
        
        {/* Filter Tabs */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="flex flex-wrap justify-center gap-2 sm:gap-4 mb-8 sm:mb-12 px-4"
        >
          {categories.map((category) => (
            <motion.button
              key={category}
              onClick={() => setFilter(category)}
              className={`px-4 sm:px-6 py-2 sm:py-3 rounded-full font-medium text-sm sm:text-base transition-all duration-300 ${
                filter === category
                  ? 'bg-gray-900 text-white shadow-lg'
                  : 'bg-white text-gray-700 hover:bg-gray-100 hover:text-gray-900 border border-gray-200'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category}
            </motion.button>
          ))}
        </motion.div>
        
        {/* Projects Grid */}
        <motion.div 
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8"
          layout
        >
          <AnimatePresence mode="popLayout">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                layout
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <ProjectCard 
                  project={project}
                  index={index}
                  isActive={activeProject === project.id}
                  onClick={() => setActiveProject(project.id)}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>
        
        {/* CTA Section */}
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="text-center mt-12 sm:mt-16 lg:mt-20"
        >
          <motion.button
            className="group bg-gray-900 text-white font-semibold px-8 sm:px-12 py-3 sm:py-4 rounded-full hover:shadow-lg hover:bg-gray-800 transition-all duration-300 text-sm sm:text-base"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <span className="relative z-10">View Full Portfolio</span>
            <motion.span 
              className="inline-block ml-2"
              animate={{ x: [0, 5, 0] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              →
            </motion.span>
          </motion.button>
        </motion.div>
      </motion.div>
    </section>
  )
}