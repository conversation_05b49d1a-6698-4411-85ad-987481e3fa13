'use client'

import { useRef, useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

interface Service {
  id: string
  title: string
  description: string
  features: string[]
  color: string
  gradient: string
  accent: string
  ctaText: string
  ctaSubtext: string
}

const services: Service[] = [
  {
    id: 'content-creation',
    title: 'Content Creation',
    description: 'Transform your brand story with compelling visual narratives that captivate and convert your audience across all digital platforms.',
    features: ['Brand Storytelling', 'Social Media Content', 'Video Production', 'Graphic Design'],
    color: '#3B82F6',
    gradient: 'from-blue-500 to-indigo-600',
    accent: '#1D4ED8',
    ctaText: 'Start Creating',
    ctaSubtext: 'Begin your content journey'
  },
  {
    id: 'photography',
    title: 'Photography',
    description: 'Capture moments that matter with our professional photography services, creating stunning visuals that tell your unique story.',
    features: ['Portrait Photography', 'Event Coverage', 'Product Photography', 'Commercial Shoots'],
    color: '#10B981',
    gradient: 'from-emerald-500 to-teal-600',
    accent: '#047857',
    ctaText: 'Book a Shoot',
    ctaSubtext: 'Schedule your session'
  },
  {
    id: 'web-development',
    title: 'Web Development',
    description: 'Build powerful, responsive websites that drive results with cutting-edge technology and user-centered design principles.',
    features: ['Custom Development', 'E-commerce Solutions', 'Mobile Optimization', 'Performance Tuning'],
    color: '#8B5CF6',
    gradient: 'from-violet-500 to-purple-600',
    accent: '#7C3AED',
    ctaText: 'Build Your Site',
    ctaSubtext: 'Start your project'
  }
]

function ServiceContent({ service, index, isActive }: { service: Service; index: number; isActive: boolean }) {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <motion.div
      className="w-screen h-full flex items-center justify-center px-4 sm:px-6 lg:px-8 py-8"
      initial={{ opacity: 0, x: 100 }}
      animate={{ 
        opacity: isActive ? 1 : 0.7,
        x: 0,
        scale: isActive ? 1 : 0.95
      }}
      transition={{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
        {/* Content Section */}
        <div className="space-y-6 lg:space-y-8">
          {/* Service Icon */}
          <motion.div
            className="w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 rounded-2xl flex items-center justify-center mb-6"
            style={{
              background: `linear-gradient(135deg, ${service.accent} 0%, ${service.color} 100%)`,
              boxShadow: `0 20px 40px ${service.accent}20`
            }}
            animate={{
              scale: isHovered ? 1.1 : 1,
              rotate: isHovered ? 5 : 0
            }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
          >
            <div className="text-white text-2xl sm:text-3xl lg:text-4xl font-bold">
              {service.title.charAt(0)}
            </div>
          </motion.div>

          {/* Title */}
          <motion.h3
            className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 leading-tight"
            style={{ letterSpacing: '-0.02em' }}
            animate={{
              y: isHovered ? -4 : 0
            }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
          >
            {service.title}
          </motion.h3>

          {/* Description */}
          <motion.p
            className="text-lg sm:text-xl lg:text-2xl text-gray-600 leading-relaxed font-light"
            style={{ lineHeight: '1.6' }}
            animate={{
              opacity: isHovered ? 1 : 0.8
            }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
          >
            {service.description}
          </motion.p>

          {/* Features Grid */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6">
            {service.features.map((feature, idx) => (
              <motion.div
                key={idx}
                className="flex items-center space-x-3 p-4 rounded-xl bg-white/60 backdrop-blur-sm border border-gray-100"
                style={{
                  boxShadow: '0 4px 20px rgba(0,0,0,0.04)'
                }}
                whileHover={{
                  scale: 1.05,
                  backgroundColor: 'rgba(255,255,255,0.8)'
                }}
                transition={{ duration: 0.2, ease: 'easeOut', delay: idx * 0.1 }}
              >
                <div
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: service.accent }}
                />
                <span className="text-sm sm:text-base font-medium text-gray-700">
                  {feature}
                </span>
              </motion.div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center lg:text-left space-y-6">
          <motion.p
            className="text-base sm:text-lg text-gray-500 font-medium"
            animate={{
              opacity: isHovered ? 1 : 0.7
            }}
            transition={{ duration: 0.4, ease: 'easeOut', delay: 0.15 }}
          >
            {service.ctaSubtext}
          </motion.p>

          <motion.button
            className="group relative inline-flex items-center justify-center px-8 sm:px-12 lg:px-16 py-4 sm:py-5 lg:py-6 text-base sm:text-lg lg:text-xl font-semibold text-white rounded-2xl transition-all duration-500 overflow-hidden"
            style={{
              background: `linear-gradient(135deg, ${service.accent} 0%, ${service.color} 100%)`,
              boxShadow: '0 20px 40px rgba(0,0,0,0.1), 0 8px 16px rgba(0,0,0,0.08)'
            }}
            whileHover={{
              scale: 1.05,
              y: -2
            }}
            whileTap={{ scale: 0.98 }}
            animate={{
              y: isHovered ? -4 : 0
            }}
            transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94], delay: 0.2 }}
          >
            {/* Background Animation */}
            <div
              className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"
              style={{
                background: `linear-gradient(135deg, ${service.color} 0%, ${service.accent} 100%)`
              }}
            />

            {/* Button Content */}
            <span className="relative z-10 flex items-center justify-center space-x-3">
              <span className="tracking-wide">{service.ctaText}</span>
              <motion.span
                className="text-xl sm:text-2xl transition-transform duration-300 group-hover:translate-x-1"
                animate={{ x: isHovered ? 6 : 0 }}
                transition={{ duration: 0.3, ease: 'easeOut' }}
              >
                →
              </motion.span>
            </span>

            {/* Shine Effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transform -skew-x-12 transition-all duration-700" />
          </motion.button>
        </div>
      </div>
    </motion.div>
  )
}

export default function AdvancedServices() {
  const containerRef = useRef<HTMLDivElement>(null)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const headerRef = useRef<HTMLDivElement>(null)
  const [currentSection, setCurrentSection] = useState(0)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    setIsLoaded(true)

    if (!containerRef.current || !scrollContainerRef.current) return

    const container = containerRef.current
    const scrollContainer = scrollContainerRef.current

    const totalSections = services.length
    const sectionWidth = window.innerWidth
    const totalScrollDistance = sectionWidth * (totalSections - 1)

    const horizontalScroll = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: 'top top',
        end: `+=${totalScrollDistance * 1.2}`,
        pin: true,
        scrub: 1.2,
        anticipatePin: 1,
        onUpdate: (self) => {
          const progress = Math.min(self.progress, 1)
          const sectionIndex = Math.floor(progress * totalSections)
          setCurrentSection(Math.min(sectionIndex, totalSections - 1))
        }
      }
    })

    horizontalScroll.to(scrollContainer, {
      x: -totalScrollDistance,
      duration: 1,
      ease: 'none'
    })

    if (headerRef.current) {
      gsap.fromTo(headerRef.current.children, {
        y: 60,
        opacity: 0,
        scale: 0.9
      }, {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 1.5,
        ease: 'power4.out',
        stagger: 0.2,
        scrollTrigger: {
          trigger: headerRef.current,
          start: 'top 85%',
          end: 'bottom 15%',
          toggleActions: 'play none none reverse'
        }
      })
    }

    const handleResize = () => {
      ScrollTrigger.refresh()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <section
      ref={containerRef}
      className="relative bg-white overflow-hidden"
      style={{ minHeight: '100vh' }}
    >
      {/* Premium Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50/80 via-white to-gray-50/60" />
        <div
          className="absolute inset-0"
          style={{
            background: 'radial-gradient(circle at 25% 25%, rgba(59,130,246,0.03) 0%, transparent 50%)'
          }}
        />
        <div
          className="absolute inset-0"
          style={{
            background: 'radial-gradient(circle at 75% 75%, rgba(16,185,129,0.03) 0%, transparent 50%)'
          }}
        />
        <div
          className="absolute inset-0"
          style={{
            background: 'radial-gradient(circle at 50% 50%, rgba(139,92,246,0.02) 0%, transparent 70%)'
          }}
        />
      </div>

      {/* Header Section */}
      <div
        ref={headerRef}
        className="absolute top-0 left-0 right-0 z-30 text-center pt-12 sm:pt-16 md:pt-20 lg:pt-24 xl:pt-28 pb-8 sm:pb-12 md:pb-16 lg:pb-20"
      >
        <motion.h2
          className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-bold text-gray-900 mb-6 sm:mb-8 md:mb-10 lg:mb-12 px-4 sm:px-6 lg:px-8 tracking-tight leading-tight"
          style={{
            textShadow: '0 2px 8px rgba(0,0,0,0.02)',
            letterSpacing: '-0.03em'
          }}
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 40 }}
          transition={{ duration: 1, ease: [0.25, 0.46, 0.45, 0.94] }}
        >
          Our Services
        </motion.h2>

        <motion.p
          className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed px-4 sm:px-6 lg:px-8 font-light"
          style={{ lineHeight: '1.6' }}
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}
          transition={{ duration: 1, delay: 0.2, ease: [0.25, 0.46, 0.45, 0.94] }}
        >
          Discover our premium solutions through an immersive horizontal journey
        </motion.p>
      </div>

      {/* Progress Indicator */}
      <motion.div
        className="absolute z-30 left-1/2 transform -translate-x-1/2 flex items-center space-x-2 sm:space-x-3"
        style={{
          top: 'clamp(180px, 25vh, 280px)'
        }}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: isLoaded ? 1 : 0, scale: isLoaded ? 1 : 0.8 }}
        transition={{ duration: 0.8, delay: 0.4 }}
      >
        {services.map((service, index) => (
          <motion.div
            key={index}
            className={`rounded-full transition-all duration-700 ease-out ${
              index === currentSection
                ? 'h-2 sm:h-2.5 w-12 sm:w-16 lg:w-20 shadow-lg'
                : 'h-1.5 sm:h-2 w-6 sm:w-8 lg:w-10'
            }`}
            style={{
              backgroundColor: index === currentSection ? service.accent : '#d1d5db',
              boxShadow: index === currentSection ? `0 4px 20px ${service.accent}40` : 'none'
            }}
            whileHover={{ scale: 1.2 }}
          />
        ))}
      </motion.div>

      {/* Content Container */}
      <div className="relative z-10 h-full" style={{ paddingTop: 'clamp(240px, 35vh, 360px)' }}>
        <div
          ref={scrollContainerRef}
          className="flex h-full"
          style={{ width: 'max-content' }}
        >
          {services.map((service, index) => (
            <ServiceContent
              key={service.id}
              service={service}
              index={index}
              isActive={currentSection === index}
            />
          ))}
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 sm:bottom-12 lg:bottom-16 left-1/2 transform -translate-x-1/2 z-30 text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
        transition={{ duration: 0.8, delay: 0.6 }}
      >
        <motion.div
          animate={{ y: [0, 8, 0] }}
          transition={{ duration: 3, repeat: Infinity, ease: 'easeInOut' }}
          className="text-gray-500 space-y-3"
        >
          <div className="text-xs sm:text-sm font-medium tracking-wider uppercase opacity-70">
            Scroll to explore
          </div>
          <motion.div
            className="text-2xl sm:text-3xl"
            animate={{ opacity: [0.4, 1, 0.4] }}
            transition={{ duration: 2.5, repeat: Infinity, ease: 'easeInOut' }}
          >
            ↓
          </motion.div>
        </motion.div>
      </motion.div>
    </section>
  )
}