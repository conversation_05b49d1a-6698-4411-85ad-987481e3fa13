'use client';

import React, { useRef, useEffect, useState } from 'react';
import { motion, useInView, useScroll, useTransform } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { fetchUnsplashPhotos, getOptimizedImageUrl } from '@/lib/unsplash';

gsap.registerPlugin(ScrollTrigger);

interface Photo {
  id: string;
  url: string;
  alt: string;
  width: number;
  height: number;
}

interface CleanPhotoGalleryProps {
  title?: string;
  subtitle?: string;
  description?: string;
  photoQuery?: string;
  category?: string;
  maxPhotos?: number;
  columns?: number;
  className?: string;
}

const CleanPhotoGallery: React.FC<CleanPhotoGalleryProps> = ({
  title = "Our Gallery",
  subtitle = "Visual Excellence",
  description = "Explore our collection of stunning visuals that showcase our creative expertise and attention to detail.",
  photoQuery = "creative design photography",
  category = "creative",
  maxPhotos = 8,
  columns = 4,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const galleryRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const isInView = useInView(containerRef, { once: true, margin: '-100px' });

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start end', 'end start']
  });

  const y = useTransform(scrollYProgress, [0, 1], [0, -30]);

  useEffect(() => {
    const loadPhotos = async () => {
      try {
        setIsLoading(true);
        const fetchedPhotos = await fetchUnsplashPhotos({
          query: photoQuery,
          per_page: maxPhotos,
          category
        });

        const processedPhotos: Photo[] = fetchedPhotos.map((photo, index) => ({
          id: photo.id,
          url: getOptimizedImageUrl(photo, { width: 600, height: 400, quality: 85 }),
          alt: photo.alt_description || `Gallery image ${index + 1}`,
          width: photo.width,
          height: photo.height
        }));

        setPhotos(processedPhotos);
      } catch (error) {
        console.error('Error loading photos:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPhotos();
  }, [photoQuery, maxPhotos, category]);

  useEffect(() => {
    if (!isInView || !galleryRef.current) return;

    const ctx = gsap.context(() => {
      // Slow, cinematic title animation
      gsap.fromTo(titleRef.current,
        {
          opacity: 0,
          y: 30
        },
        {
          opacity: 1,
          y: 0,
          duration: 1.5,
          ease: 'power2.out'
        }
      );

      // Stagger animation for gallery items
      const items = galleryRef.current?.children;
      if (!items) return;

      gsap.fromTo(items,
        {
          opacity: 0,
          y: 40
        },
        {
          opacity: 1,
          y: 0,
          duration: 1.2,
          stagger: 0.15,
          ease: 'power2.out',
          delay: 0.5
        }
      );
    }, galleryRef);

    return () => ctx.revert();
  }, [isInView, photos]);

  return (
    <motion.section
      ref={containerRef}
      className={`py-24 md:py-32 relative overflow-hidden bg-white ${className}`}
      style={{ y }}
    >
      {/* Minimal background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-20 w-32 h-32 bg-gray-100 rounded-full opacity-30" />
        <div className="absolute bottom-20 left-20 w-24 h-24 bg-gray-900 opacity-5 rotate-45" />
        <div className="absolute top-1/2 left-1/4 w-px h-32 bg-gray-200" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6">
        {/* Clean Header */}
        <div className="text-center mb-20">
          <motion.p
            className="text-sm md:text-base font-medium text-gray-500 tracking-[0.2em] uppercase mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ delay: 0.2, duration: 1 }}
          >
            {subtitle}
          </motion.p>

          <h2
            ref={titleRef}
            className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 mb-8"
          >
            {title}
          </h2>

          <motion.p
            className="text-lg text-gray-600 max-w-2xl mx-auto font-light leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ delay: 0.6, duration: 1 }}
          >
            {description}
          </motion.p>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[...Array(8)].map((_, index) => (
              <div
                key={index}
                className="aspect-[4/3] bg-gray-100 animate-pulse"
              />
            ))}
          </div>
        )}

        {/* Clean Photo Grid */}
        {!isLoading && (
          <div
            ref={galleryRef}
            className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
          >
            {photos.map((photo, index) => (
              <motion.div
                key={photo.id}
                className="group cursor-pointer"
                whileHover={{ y: -5 }}
                transition={{ duration: 0.4, ease: "easeOut" }}
              >
                <div className="relative overflow-hidden bg-gray-100 aspect-[4/3]">
                  <img
                    src={photo.url}
                    alt={photo.alt}
                    className="w-full h-full object-cover transition-all duration-700 group-hover:scale-105"
                    loading="lazy"
                  />

                  {/* Subtle overlay on hover */}
                  <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-10 transition-opacity duration-500" />

                  {/* Minimal corner accent */}
                  <div className="absolute top-4 right-4 w-6 h-6 border-t border-r border-white opacity-0 group-hover:opacity-60 transition-opacity duration-500" />
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!isLoading && photos.length === 0 && (
          <div className="text-center py-16">
            <p className="text-gray-500 text-lg font-light">No photos available at the moment.</p>
          </div>
        )}
      </div>
    </motion.section>
  );
};

export default CleanPhotoGallery;
