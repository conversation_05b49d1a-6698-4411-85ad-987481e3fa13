'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, useScroll, useTransform, useInView } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { UnsplashPhoto, fetchRandomPhoto, getOptimizedImageUrl } from '@/lib/unsplash'
import { useSmoothScroll } from '@/hooks/useSmoothScroll'

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger)
}

interface HeroPhotoProps {
  title?: string
  subtitle?: string
  ctaText?: string
  ctaLink?: string
  height?: 'screen' | 'large' | 'medium'
  overlay?: 'dark' | 'light' | 'gradient' | 'none'
  photoQuery?: string
  className?: string
}

export default function HeroPhoto({
  title = 'Visual Excellence',
  subtitle = 'Creating stunning visuals that tell your story',
  ctaText = 'View Our Work',
  ctaLink = '/portfolio',
  height = 'screen',
  overlay = 'gradient',
  photoQuery = 'creative agency design',
  className = ''
}: HeroPhotoProps) {
  const [photo, setPhoto] = useState<UnsplashPhoto | null>(null)
  const [imageLoaded, setImageLoaded] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const containerRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  const { scrollToTarget } = useSmoothScroll()
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start start', 'end start']
  })

  const y = useTransform(scrollYProgress, [0, 1], ['0%', '50%'])
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0])
  const scale = useTransform(scrollYProgress, [0, 1], [1, 1.2])

  // Load hero photo
  useEffect(() => {
    const loadPhoto = async () => {
      setIsLoading(true)
      try {
        const fetchedPhoto = await fetchRandomPhoto(photoQuery)
        setPhoto(fetchedPhoto)
      } catch (error) {
        console.error('Error loading hero photo:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadPhoto()
  }, [photoQuery])

  // GSAP animations
  useEffect(() => {
    if (!contentRef.current || !imageLoaded) return

    const content = contentRef.current
    const tl = gsap.timeline()

    // Initial state
    gsap.set(content.children, {
      y: 100,
      opacity: 0
    })

    // Animate in
    tl.to(content.children, {
      y: 0,
      opacity: 1,
      duration: 1.2,
      stagger: 0.2,
      ease: 'power3.out',
      delay: 0.5
    })

    return () => {
      tl.kill()
    }
  }, [imageLoaded])

  const heightClasses = {
    screen: 'h-screen',
    large: 'h-[80vh]',
    medium: 'h-[60vh]'
  }

  const overlayClasses = {
    dark: 'bg-black/50',
    light: 'bg-white/30',
    gradient: 'bg-gradient-to-t from-black/70 via-black/20 to-transparent',
    none: ''
  }

  return (
    <section
      ref={containerRef}
      className={`relative ${heightClasses[height]} overflow-hidden pt-20 lg:pt-24 ${className}`}
    >
      {/* Background Image */}
      <motion.div
        className="absolute inset-0"
        style={{ scale }}
      >
        {photo && (
          <motion.img
            ref={imageRef}
            src={getOptimizedImageUrl(photo, { width: 1920, quality: 85 })}
            alt={photo.alt_description || photo.description || 'Hero image'}
            className="w-full h-full object-cover"
            style={{ y }}
            onLoad={() => setImageLoaded(true)}
            initial={{ scale: 1.1, opacity: 0 }}
            animate={{
              scale: 1,
              opacity: imageLoaded ? 1 : 0
            }}
            transition={{ duration: 1.5, ease: 'easeOut' }}
          />
        )}

        {/* Loading placeholder */}
        {isLoading && (
          <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
            <motion.div
              className="w-12 h-12 border-2 border-gray-400 border-t-transparent rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            />
          </div>
        )}
      </motion.div>

      {/* Overlay */}
      {overlay !== 'none' && (
        <motion.div
          className={`absolute inset-0 ${overlayClasses[overlay]}`}
          initial={{ opacity: 0 }}
          animate={{ opacity: imageLoaded ? 1 : 0 }}
          transition={{ duration: 1, delay: 0.5 }}
        />
      )}

      {/* Content */}
      <motion.div
        ref={contentRef}
        className="relative z-10 h-full flex items-center justify-center"
        style={{ opacity }}
      >
        <div className="text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
          {/* Title */}
          <motion.h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-white mb-4 sm:mb-6 leading-tight">
            {title}
          </motion.h1>

          {/* Subtitle */}
          <motion.p className="text-lg sm:text-xl md:text-2xl text-white/90 mb-8 sm:mb-12 max-w-2xl mx-auto leading-relaxed">
            {subtitle}
          </motion.p>

          {/* CTA Button */}
          <motion.button
            onClick={() => scrollToTarget(ctaLink)}
            className="group relative inline-flex items-center justify-center px-8 sm:px-12 py-4 sm:py-6 text-base sm:text-lg font-semibold text-white bg-white/10 backdrop-blur-sm border border-white/20 rounded-full hover:bg-white/20 transition-all duration-300 overflow-hidden"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {/* Button background effect */}
            <motion.div
              className="absolute inset-0 bg-white/10"
              initial={{ x: '-100%' }}
              whileHover={{ x: '0%' }}
              transition={{ duration: 0.3 }}
            />

            <span className="relative z-10 mr-2">{ctaText}</span>

            <motion.svg
              className="relative z-10 w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              initial={{ x: 0 }}
              whileHover={{ x: 5 }}
              transition={{ duration: 0.3 }}
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </motion.svg>
          </motion.button>
        </div>
      </motion.div>

      {/* Scroll indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: imageLoaded ? 1 : 0, y: imageLoaded ? 0 : 20 }}
        transition={{ duration: 1, delay: 2 }}
      >
        <motion.div
          className="flex flex-col items-center cursor-pointer group"
          onClick={() => scrollToTarget('#next-section')}
          whileHover={{ y: -5 }}
        >
          <span className="text-white/70 text-sm mb-2 group-hover:text-white transition-colors">
            Scroll to explore
          </span>
          <motion.div
            className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center group-hover:border-white/50 transition-colors"
            animate={{ y: [0, 5, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
          >
            <motion.div
              className="w-1 h-3 bg-white/50 rounded-full mt-2 group-hover:bg-white/70 transition-colors"
              animate={{ y: [0, 8, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
            />
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Photo credit */}
      {photo && imageLoaded && (
        <motion.div
          className="absolute bottom-4 right-4 z-10"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 3 }}
        >
          <div className="bg-black/30 backdrop-blur-sm rounded-lg px-3 py-2">
            <p className="text-white/70 text-xs">
              Photo by{' '}
              <a
                href={`https://unsplash.com/@${photo.user.username}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-white/90 hover:text-white transition-colors underline"
              >
                {photo.user.name}
              </a>
              {' '}on{' '}
              <a
                href="https://unsplash.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white/90 hover:text-white transition-colors underline"
              >
                Unsplash
              </a>
            </p>
          </div>
        </motion.div>
      )}
    </section>
  )
}