'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence, useInView, useScroll, useTransform } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { UnsplashPhoto, fetchUnsplashPhotos, getOptimizedImageUrl, PHOTO_CATEGORIES, PhotoCategory } from '@/lib/unsplash'
import { useSmoothScroll } from '@/hooks/useSmoothScroll'

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger)
}

interface PhotoGalleryProps {
  category?: PhotoCategory
  title?: string
  subtitle?: string
  maxPhotos?: number
  columns?: 2 | 3 | 4
  showFilters?: boolean
  className?: string
}

interface PhotoItemProps {
  photo: UnsplashPhoto
  index: number
  onClick: () => void
  isLoaded: boolean
}

// Individual photo item with animations
function PhotoItem({ photo, index, onClick, isLoaded }: PhotoItemProps) {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const itemRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!itemRef.current || !isLoaded) return

    const element = itemRef.current

    // GSAP scroll animation
    gsap.fromTo(element, {
      y: 60,
      opacity: 0,
      scale: 0.9
    }, {
      y: 0,
      opacity: 1,
      scale: 1,
      duration: 0.8,
      ease: 'power3.out',
      delay: index * 0.1,
      scrollTrigger: {
        trigger: element,
        start: 'top 85%',
        end: 'bottom 15%',
        toggleActions: 'play none none reverse'
      }
    })

    return () => {
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === element) {
          trigger.kill()
        }
      })
    }
  }, [index, isLoaded])

  const aspectRatio = photo.height / photo.width
  const height = aspectRatio > 1.5 ? 'h-80 sm:h-96' : aspectRatio > 1 ? 'h-64 sm:h-80' : 'h-48 sm:h-64'

  return (
    <motion.div
      ref={itemRef}
      className={`group relative cursor-pointer overflow-hidden rounded-2xl bg-gray-100 ${height}`}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ y: -8 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
    >
      {/* Image */}
      <motion.img
        src={getOptimizedImageUrl(photo, { width: 600, quality: 85 })}
        alt={photo.alt_description || photo.description || 'Gallery image'}
        className="w-full h-full object-cover transition-all duration-700"
        style={{
          transform: isHovered ? 'scale(1.1)' : 'scale(1)',
          filter: isHovered ? 'brightness(0.8)' : 'brightness(1)'
        }}
        onLoad={() => setImageLoaded(true)}
        loading="lazy"
      />

      {/* Loading placeholder */}
      <AnimatePresence>
        {!imageLoaded && (
          <motion.div
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-gray-200 flex items-center justify-center"
          >
            <motion.div
              className="w-8 h-8 border-2 border-gray-400 border-t-transparent rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Overlay */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"
        initial={{ opacity: 0 }}
        animate={{ opacity: isHovered ? 1 : 0 }}
        transition={{ duration: 0.3 }}
      />

      {/* Content overlay */}
      <motion.div
        className="absolute bottom-0 left-0 right-0 p-4 text-white"
        initial={{ y: 20, opacity: 0 }}
        animate={{ 
          y: isHovered ? 0 : 20, 
          opacity: isHovered ? 1 : 0 
        }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        {photo.description && (
          <p className="text-sm font-medium mb-1 line-clamp-2">
            {photo.description}
          </p>
        )}
        <p className="text-xs opacity-80">
          by {photo.user.name}
        </p>
      </motion.div>

      {/* Hover indicator */}
      <motion.div
        className="absolute top-4 right-4 w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center"
        initial={{ scale: 0, opacity: 0 }}
        animate={{ 
          scale: isHovered ? 1 : 0, 
          opacity: isHovered ? 1 : 0 
        }}
        transition={{ duration: 0.2 }}
      >
        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </motion.div>
    </motion.div>
  )
}

// Lightbox modal for full-size image viewing
function PhotoLightbox({ photo, isOpen, onClose }: { photo: UnsplashPhoto | null; isOpen: boolean; onClose: () => void }) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  if (!photo) return null

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          {/* Backdrop */}
          <motion.div
            className="absolute inset-0 bg-black/90 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          />

          {/* Image container */}
          <motion.div
            className="relative max-w-7xl max-h-full"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            onClick={(e) => e.stopPropagation()}
          >
            <img
              src={getOptimizedImageUrl(photo, { width: 1200, quality: 90 })}
              alt={photo.alt_description || photo.description || 'Gallery image'}
              className="max-w-full max-h-full object-contain rounded-lg"
            />

            {/* Close button */}
            <motion.button
              className="absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors"
              onClick={onClose}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </motion.button>

            {/* Photo info */}
            <motion.div
              className="absolute bottom-4 left-4 right-4 bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {photo.description && (
                <h3 className="font-semibold mb-1">{photo.description}</h3>
              )}
              <p className="text-sm opacity-80">
                Photo by {photo.user.name} on Unsplash
              </p>
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Main PhotoGallery component
export default function PhotoGallery({
  category = 'creative',
  title = 'Our Work',
  subtitle = 'Capturing moments and creating visual stories',
  maxPhotos = 12,
  columns = 3,
  showFilters = true,
  className = ''
}: PhotoGalleryProps) {
  const [photos, setPhotos] = useState<UnsplashPhoto[]>([])
  const [filteredPhotos, setFilteredPhotos] = useState<UnsplashPhoto[]>([])
  const [selectedCategory, setSelectedCategory] = useState<PhotoCategory>(category)
  const [selectedPhoto, setSelectedPhoto] = useState<UnsplashPhoto | null>(null)
  const [isLightboxOpen, setIsLightboxOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isLoaded, setIsLoaded] = useState(false)

  const containerRef = useRef<HTMLDivElement>(null)
  const headerRef = useRef<HTMLDivElement>(null)
  const isInView = useInView(containerRef, { once: true, margin: '-100px' })
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start end', 'end start']
  })
  const y = useTransform(scrollYProgress, [0, 1], ['0%', '-10%'])

  // Load photos
  useEffect(() => {
    const loadPhotos = async () => {
      setIsLoading(true)
      try {
        const fetchedPhotos = await fetchUnsplashPhotos({
          query: PHOTO_CATEGORIES[selectedCategory],
          per_page: maxPhotos
        })
        setPhotos(fetchedPhotos)
        setFilteredPhotos(fetchedPhotos)
      } catch (error) {
        console.error('Error loading photos:', error)
      } finally {
        setIsLoading(false)
        setTimeout(() => setIsLoaded(true), 100)
      }
    }

    loadPhotos()
  }, [selectedCategory, maxPhotos])

  // Filter photos by category
  const handleCategoryChange = async (newCategory: PhotoCategory) => {
    if (newCategory === selectedCategory) return
    
    setSelectedCategory(newCategory)
    setIsLoaded(false)
  }

  // Open lightbox
  const openLightbox = (photo: UnsplashPhoto) => {
    setSelectedPhoto(photo)
    setIsLightboxOpen(true)
  }

  // Close lightbox
  const closeLightbox = () => {
    setIsLightboxOpen(false)
    setTimeout(() => setSelectedPhoto(null), 300)
  }

  const gridCols = {
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
  }

  return (
    <section
      ref={containerRef}
      className={`relative py-16 sm:py-20 lg:py-24 bg-white overflow-hidden ${className}`}
    >
      {/* Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50/50 via-white to-gray-50/30" />
      </div>

      <motion.div 
        className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        style={{ y }}
      >
        {/* Header */}
        <div ref={headerRef} className="text-center mb-12 sm:mb-16">
          <motion.h2
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 sm:mb-6"
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 0.8 }}
          >
            {title}
          </motion.h2>
          <motion.p
            className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            {subtitle}
          </motion.p>
        </div>

        {/* Category filters */}
        {showFilters && (
          <motion.div
            className="flex flex-wrap justify-center gap-2 sm:gap-4 mb-8 sm:mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {Object.keys(PHOTO_CATEGORIES).map((cat) => {
              const categoryKey = cat as PhotoCategory
              return (
                <motion.button
                  key={categoryKey}
                  onClick={() => handleCategoryChange(categoryKey)}
                  className={`px-4 sm:px-6 py-2 sm:py-3 rounded-full font-medium text-sm sm:text-base transition-all duration-300 capitalize ${
                    selectedCategory === categoryKey
                      ? 'bg-gray-900 text-white shadow-lg'
                      : 'bg-white text-gray-700 hover:bg-gray-100 hover:text-gray-900 border border-gray-200'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  disabled={isLoading}
                >
                  {categoryKey}
                </motion.button>
              )
            })}
          </motion.div>
        )}

        {/* Loading state */}
        {isLoading && (
          <div className="flex justify-center items-center py-12">
            <motion.div
              className="w-12 h-12 border-2 border-gray-300 border-t-gray-900 rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            />
          </div>
        )}

        {/* Photo grid */}
        {!isLoading && (
          <motion.div
            className={`grid ${gridCols[columns]} gap-4 sm:gap-6 lg:gap-8`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <AnimatePresence mode="popLayout">
              {filteredPhotos.map((photo, index) => (
                <motion.div
                  key={`${selectedCategory}-${photo.id}`}
                  layout
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.5, delay: index * 0.05 }}
                >
                  <PhotoItem
                    photo={photo}
                    index={index}
                    onClick={() => openLightbox(photo)}
                    isLoaded={isLoaded}
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>
        )}

        {/* Empty state */}
        {!isLoading && filteredPhotos.length === 0 && (
          <motion.div
            className="text-center py-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <p className="text-gray-500 text-lg">No photos found for this category.</p>
          </motion.div>
        )}
      </motion.div>

      {/* Lightbox */}
      <PhotoLightbox
        photo={selectedPhoto}
        isOpen={isLightboxOpen}
        onClose={closeLightbox}
      />
    </section>
  )
}