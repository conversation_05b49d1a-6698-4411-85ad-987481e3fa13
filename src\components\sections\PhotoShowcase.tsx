'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence, useInView, useScroll, useTransform } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { UnsplashPhoto, fetchUnsplashPhotos, getOptimizedImageUrl, PHOTO_CATEGORIES, PhotoCategory } from '@/lib/unsplash'
import { useSmoothScroll } from '@/hooks/useSmoothScroll'

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger)
}

interface ShowcaseItem {
  id: string
  title: string
  description: string
  category: string
  photo: UnsplashPhoto
  link?: string
}

interface PhotoShowcaseProps {
  title?: string
  subtitle?: string
  category?: PhotoCategory
  maxItems?: number
  layout?: 'grid' | 'masonry' | 'carousel'
  showCategories?: boolean
  className?: string
}

// Individual showcase item component
function ShowcaseItem({ item, index, layout, onClick }: { 
  item: ShowcaseItem
  index: number
  layout: 'grid' | 'masonry' | 'carousel'
  onClick: () => void 
}) {
  const [isHovered, setIsHovered] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)
  const itemRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!itemRef.current || !imageLoaded) return

    const element = itemRef.current

    // GSAP scroll animation
    gsap.fromTo(element, {
      y: 80,
      opacity: 0,
      scale: 0.95
    }, {
      y: 0,
      opacity: 1,
      scale: 1,
      duration: 1,
      ease: 'power3.out',
      delay: index * 0.15,
      scrollTrigger: {
        trigger: element,
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse'
      }
    })

    return () => {
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === element) {
          trigger.kill()
        }
      })
    }
  }, [index, imageLoaded])

  const aspectRatio = item.photo.height / item.photo.width
  const heightClass = layout === 'masonry' 
    ? aspectRatio > 1.3 ? 'h-96' : aspectRatio > 1 ? 'h-80' : 'h-64'
    : 'h-80'

  return (
    <motion.div
      ref={itemRef}
      className={`group relative cursor-pointer overflow-hidden rounded-2xl bg-gray-100 ${heightClass}`}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ y: -12 }}
      transition={{ duration: 0.4, ease: 'easeOut' }}
    >
      {/* Image */}
      <motion.div
        className="relative w-full h-full overflow-hidden"
        initial={{ scale: 1.1 }}
        animate={{ scale: isHovered ? 1.15 : 1.1 }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
      >
        <img
          src={getOptimizedImageUrl(item.photo, { width: 800, quality: 85 })}
          alt={item.photo.alt_description || item.title}
          className="w-full h-full object-cover"
          onLoad={() => setImageLoaded(true)}
          loading="lazy"
        />

        {/* Loading placeholder */}
        <AnimatePresence>
          {!imageLoaded && (
            <motion.div
              initial={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-gray-200 flex items-center justify-center"
            >
              <motion.div
                className="w-8 h-8 border-2 border-gray-400 border-t-transparent rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Overlay */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"
        initial={{ opacity: 0.6 }}
        animate={{ opacity: isHovered ? 0.8 : 0.6 }}
        transition={{ duration: 0.3 }}
      />

      {/* Content */}
      <motion.div
        className="absolute inset-0 p-6 flex flex-col justify-end text-white"
        initial={{ y: 20 }}
        animate={{ y: isHovered ? 0 : 20 }}
        transition={{ duration: 0.3 }}
      >
        {/* Category */}
        <motion.span
          className="inline-block px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-xs font-medium mb-3 w-fit"
          initial={{ opacity: 0, y: 10 }}
          animate={{ 
            opacity: isHovered ? 1 : 0, 
            y: isHovered ? 0 : 10 
          }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          {item.category}
        </motion.span>

        {/* Title */}
        <motion.h3
          className="text-xl font-bold mb-2 line-clamp-2"
          initial={{ y: 10 }}
          animate={{ y: isHovered ? 0 : 10 }}
          transition={{ duration: 0.3, delay: 0.05 }}
        >
          {item.title}
        </motion.h3>

        {/* Description */}
        <motion.p
          className="text-sm text-white/80 line-clamp-3 mb-4"
          initial={{ opacity: 0, y: 10 }}
          animate={{ 
            opacity: isHovered ? 1 : 0, 
            y: isHovered ? 0 : 10 
          }}
          transition={{ duration: 0.3, delay: 0.15 }}
        >
          {item.description}
        </motion.p>

        {/* View button */}
        <motion.div
          className="flex items-center text-sm font-medium"
          initial={{ opacity: 0, x: -10 }}
          animate={{ 
            opacity: isHovered ? 1 : 0, 
            x: isHovered ? 0 : -10 
          }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <span className="mr-2">View Project</span>
          <motion.svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            animate={{ x: isHovered ? 5 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
          </motion.svg>
        </motion.div>
      </motion.div>

      {/* Hover indicator */}
      <motion.div
        className="absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center"
        initial={{ scale: 0, opacity: 0 }}
        animate={{ 
          scale: isHovered ? 1 : 0, 
          opacity: isHovered ? 1 : 0 
        }}
        transition={{ duration: 0.2 }}
      >
        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
        </svg>
      </motion.div>
    </motion.div>
  )
}

// Main PhotoShowcase component
export default function PhotoShowcase({
  title = 'Featured Work',
  subtitle = 'Showcasing our latest creative projects and visual stories',
  category = 'creative',
  maxItems = 6,
  layout = 'grid',
  showCategories = true,
  className = ''
}: PhotoShowcaseProps) {
  const [showcaseItems, setShowcaseItems] = useState<ShowcaseItem[]>([])
  const [selectedCategory, setSelectedCategory] = useState<PhotoCategory>(category)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedItem, setSelectedItem] = useState<ShowcaseItem | null>(null)

  const containerRef = useRef<HTMLDivElement>(null)
  const headerRef = useRef<HTMLDivElement>(null)
  const isInView = useInView(containerRef, { once: true, margin: '-100px' })
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start end', 'end start']
  })
  const y = useTransform(scrollYProgress, [0, 1], ['0%', '-5%'])
  const { scrollToTarget } = useSmoothScroll()

  // Sample project data
  const projectTitles = [
    'Brand Identity Design',
    'Digital Marketing Campaign',
    'Website Redesign',
    'Product Photography',
    'Creative Direction',
    'Visual Storytelling',
    'User Experience Design',
    'Content Strategy',
    'Social Media Campaign',
    'Print Design'
  ]

  const projectDescriptions = [
    'A comprehensive brand identity that captures the essence of modern innovation.',
    'Strategic digital campaign that increased engagement by 300% across all platforms.',
    'Complete website overhaul focusing on user experience and conversion optimization.',
    'Professional product photography that showcases quality and craftsmanship.',
    'Creative direction that brings brand vision to life through compelling visuals.',
    'Narrative-driven content that connects with audiences on an emotional level.',
    'User-centered design approach that improves usability and satisfaction.',
    'Content strategy that drives engagement and builds lasting relationships.',
    'Social media campaign that amplifies brand presence and community growth.',
    'Print design solutions that make a lasting impression in the physical world.'
  ]

  // Load photos and create showcase items
  useEffect(() => {
    const loadShowcaseItems = async () => {
      setIsLoading(true)
      try {
        const photos = await fetchUnsplashPhotos({
          query: PHOTO_CATEGORIES[selectedCategory],
          per_page: maxItems
        })

        const items: ShowcaseItem[] = photos.map((photo, index) => ({
          id: photo.id,
          title: projectTitles[index % projectTitles.length],
          description: projectDescriptions[index % projectDescriptions.length],
          category: selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1),
          photo,
          link: `/portfolio/${photo.id}`
        }))

        setShowcaseItems(items)
      } catch (error) {
        console.error('Error loading showcase items:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadShowcaseItems()
  }, [selectedCategory, maxItems])

  const handleItemClick = (item: ShowcaseItem) => {
    setSelectedItem(item)
    if (item.link) {
      scrollToTarget(item.link)
    }
  }

  const handleCategoryChange = (newCategory: PhotoCategory) => {
    if (newCategory === selectedCategory) return
    setSelectedCategory(newCategory)
  }

  const getGridClasses = () => {
    switch (layout) {
      case 'masonry':
        return 'columns-1 sm:columns-2 lg:columns-3 gap-6 space-y-6'
      case 'carousel':
        return 'flex gap-6 overflow-x-auto pb-4 snap-x snap-mandatory'
      default:
        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'
    }
  }

  return (
    <section
      ref={containerRef}
      className={`relative py-16 sm:py-20 lg:py-24 bg-gray-50 overflow-hidden ${className}`}
    >
      {/* Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-gray-100" />
      </div>

      <motion.div 
        className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        style={{ y }}
      >
        {/* Header */}
        <div ref={headerRef} className="text-center mb-12 sm:mb-16">
          <motion.h2
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 sm:mb-6"
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 0.8 }}
          >
            {title}
          </motion.h2>
          <motion.p
            className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            {subtitle}
          </motion.p>
        </div>

        {/* Category filters */}
        {showCategories && (
          <motion.div
            className="flex flex-wrap justify-center gap-2 sm:gap-4 mb-8 sm:mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {Object.keys(PHOTO_CATEGORIES).slice(0, 5).map((cat) => {
              const categoryKey = cat as PhotoCategory
              return (
                <motion.button
                  key={categoryKey}
                  onClick={() => handleCategoryChange(categoryKey)}
                  className={`px-4 sm:px-6 py-2 sm:py-3 rounded-full font-medium text-sm sm:text-base transition-all duration-300 capitalize ${
                    selectedCategory === categoryKey
                      ? 'bg-gray-900 text-white shadow-lg'
                      : 'bg-white text-gray-700 hover:bg-gray-100 hover:text-gray-900 border border-gray-200 shadow-sm'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  disabled={isLoading}
                >
                  {categoryKey}
                </motion.button>
              )
            })}
          </motion.div>
        )}

        {/* Loading state */}
        {isLoading && (
          <div className="flex justify-center items-center py-12">
            <motion.div
              className="w-12 h-12 border-2 border-gray-300 border-t-gray-900 rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            />
          </div>
        )}

        {/* Showcase grid */}
        {!isLoading && (
          <motion.div
            className={getGridClasses()}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <AnimatePresence mode="popLayout">
              {showcaseItems.map((item, index) => (
                <motion.div
                  key={`${selectedCategory}-${item.id}`}
                  layout
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.5, delay: index * 0.05 }}
                  className={layout === 'carousel' ? 'flex-none w-80 snap-start' : ''}
                >
                  <ShowcaseItem
                    item={item}
                    index={index}
                    layout={layout}
                    onClick={() => handleItemClick(item)}
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>
        )}

        {/* View all button */}
        {!isLoading && showcaseItems.length > 0 && (
          <motion.div
            className="text-center mt-12 sm:mt-16"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <motion.button
              onClick={() => scrollToTarget('/portfolio')}
              className="group inline-flex items-center justify-center px-8 sm:px-12 py-4 sm:py-6 text-base sm:text-lg font-semibold text-white bg-gray-900 rounded-full hover:bg-gray-800 transition-all duration-300 shadow-lg hover:shadow-xl"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="mr-2">View All Projects</span>
              <motion.svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                initial={{ x: 0 }}
                whileHover={{ x: 5 }}
                transition={{ duration: 0.3 }}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </motion.svg>
            </motion.button>
          </motion.div>
        )}
      </motion.div>
    </section>
  )
}