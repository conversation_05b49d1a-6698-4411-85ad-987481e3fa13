'use client'

import { useEffect } from 'react'
import { use<PERSON>eni<PERSON> } from 'lenis/react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger)
}

export function useSmoothScroll() {
  const lenis = useLenis()

  useEffect(() => {
    if (!lenis) return

    // Integrate Lenis with GSAP ScrollTrigger
    lenis.on('scroll', ScrollTrigger.update)

    gsap.ticker.add((time) => {
      lenis.raf(time * 1000)
    })

    gsap.ticker.lagSmoothing(0)

    // Cleanup
    return () => {
      gsap.ticker.remove(lenis.raf)
    }
  }, [lenis])

  // Utility functions for smooth scrolling
  const scrollTo = (target: string | number, options?: { duration?: number; easing?: (t: number) => number; [key: string]: unknown }) => {
    if (!lenis) return
    lenis.scrollTo(target, {
      duration: 1.0,
      easing: (t: number) => t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2,
      ...options,
    })
  }

  const scrollToTop = () => {
    scrollTo(0)
  }

  const scrollToElement = (selector: string) => {
    scrollTo(selector)
  }

  const scrollToTarget = (target: string) => {
    // If it's a route/URL, navigate to it
    if (target.startsWith('/')) {
      window.location.href = target
      return
    }
    // Otherwise treat it as an element selector
    scrollTo(target)
  }

  return {
    lenis,
    scrollTo,
    scrollToTop,
    scrollToElement,
    scrollToTarget,
  }
}