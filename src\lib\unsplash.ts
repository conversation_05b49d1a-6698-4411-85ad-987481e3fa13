'use client'

// Unsplash API configuration
const UNSPLASH_ACCESS_KEY = process.env.NEXT_PUBLIC_UNSPLASH_ACCESS_KEY || 'demo'
const UNSPLASH_BASE_URL = 'https://api.unsplash.com'

export interface UnsplashPhoto {
  id: string
  urls: {
    raw: string
    full: string
    regular: string
    small: string
    thumb: string
  }
  alt_description: string | null
  description: string | null
  user: {
    name: string
    username: string
  }
  width: number
  height: number
  color: string
  blur_hash?: string
}

export interface UnsplashSearchParams {
  query: string
  page?: number
  per_page?: number
  orientation?: 'landscape' | 'portrait' | 'squarish'
  color?: 'black_and_white' | 'black' | 'white' | 'yellow' | 'orange' | 'red' | 'purple' | 'magenta' | 'green' | 'teal' | 'blue'
}

// Fallback photos for demo purposes (high-quality Unsplash URLs)
const DEMO_PHOTOS: UnsplashPhoto[] = [
  {
    id: 'demo-1',
    urls: {
      raw: 'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?ixlib=rb-4.0.3',
      full: 'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb',
      regular: 'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=1080',
      small: 'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=400',
      thumb: 'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=200'
    },
    alt_description: 'Modern architecture photography',
    description: 'Contemporary building design',
    user: { name: 'Architecture Pro', username: 'archpro' },
    width: 1080,
    height: 1350,
    color: '#2c3e50'
  },
  {
    id: 'demo-2',
    urls: {
      raw: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3',
      full: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb',
      regular: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=1080',
      small: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=400',
      thumb: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=200'
    },
    alt_description: 'Professional photography session',
    description: 'Behind the scenes photography',
    user: { name: 'Photo Studio', username: 'photostudio' },
    width: 1080,
    height: 720,
    color: '#34495e'
  },
  {
    id: 'demo-3',
    urls: {
      raw: 'https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3',
      full: 'https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb',
      regular: 'https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=1080',
      small: 'https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=400',
      thumb: 'https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=200'
    },
    alt_description: 'Creative workspace design',
    description: 'Modern office environment',
    user: { name: 'Design Studio', username: 'designstudio' },
    width: 1080,
    height: 720,
    color: '#3498db'
  },
  {
    id: 'demo-4',
    urls: {
      raw: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3',
      full: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb',
      regular: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=1080',
      small: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=400',
      thumb: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=200'
    },
    alt_description: 'Business meeting photography',
    description: 'Professional team collaboration',
    user: { name: 'Business Pro', username: 'bizpro' },
    width: 1080,
    height: 720,
    color: '#e74c3c'
  },
  {
    id: 'demo-5',
    urls: {
      raw: 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3',
      full: 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb',
      regular: 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=1080',
      small: 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=400',
      thumb: 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=200'
    },
    alt_description: 'Modern office space',
    description: 'Contemporary workplace design',
    user: { name: 'Office Design', username: 'officedesign' },
    width: 1080,
    height: 720,
    color: '#9b59b6'
  },
  {
    id: 'demo-6',
    urls: {
      raw: 'https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3',
      full: 'https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb',
      regular: 'https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=1080',
      small: 'https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=400',
      thumb: 'https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=srgb&w=200'
    },
    alt_description: 'Creative team collaboration',
    description: 'Design team working together',
    user: { name: 'Creative Team', username: 'creativeteam' },
    width: 1080,
    height: 720,
    color: '#1abc9c'
  }
]

// Fetch photos from Unsplash API
export async function fetchUnsplashPhotos(params: UnsplashSearchParams): Promise<UnsplashPhoto[]> {
  // If no API key is provided, return demo photos
  if (UNSPLASH_ACCESS_KEY === 'demo') {
    console.log('Using demo photos. Set NEXT_PUBLIC_UNSPLASH_ACCESS_KEY for live Unsplash integration.')
    return DEMO_PHOTOS
  }

  try {
    const searchParams = new URLSearchParams({
      query: params.query,
      page: (params.page || 1).toString(),
      per_page: (params.per_page || 12).toString(),
      ...(params.orientation && { orientation: params.orientation }),
      ...(params.color && { color: params.color })
    })

    const response = await fetch(
      `${UNSPLASH_BASE_URL}/search/photos?${searchParams}`,
      {
        headers: {
          'Authorization': `Client-ID ${UNSPLASH_ACCESS_KEY}`,
          'Accept-Version': 'v1'
        }
      }
    )

    if (!response.ok) {
      throw new Error(`Unsplash API error: ${response.status}`)
    }

    const data = await response.json()
    return data.results || []
  } catch (error) {
    console.error('Error fetching Unsplash photos:', error)
    // Fallback to demo photos on error
    return DEMO_PHOTOS
  }
}

// Get a single random photo
export async function fetchRandomPhoto(query?: string): Promise<UnsplashPhoto | null> {
  if (UNSPLASH_ACCESS_KEY === 'demo') {
    const randomIndex = Math.floor(Math.random() * DEMO_PHOTOS.length)
    return DEMO_PHOTOS[randomIndex]
  }

  try {
    const searchParams = new URLSearchParams({
      ...(query && { query })
    })

    const response = await fetch(
      `${UNSPLASH_BASE_URL}/photos/random?${searchParams}`,
      {
        headers: {
          'Authorization': `Client-ID ${UNSPLASH_ACCESS_KEY}`,
          'Accept-Version': 'v1'
        }
      }
    )

    if (!response.ok) {
      throw new Error(`Unsplash API error: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error('Error fetching random photo:', error)
    return null
  }
}

// Generate optimized image URL with custom parameters
export function getOptimizedImageUrl(
  photo: UnsplashPhoto,
  options: {
    width?: number
    height?: number
    quality?: number
    format?: 'jpg' | 'webp' | 'avif'
    fit?: 'crop' | 'fill' | 'scale'
  } = {}
): string {
  const {
    width = 800,
    height,
    quality = 80,
    format = 'jpg',
    fit = 'crop'
  } = options

  const params = new URLSearchParams({
    'ixlib': 'rb-4.0.3',
    'q': quality.toString(),
    'fm': format,
    'fit': fit,
    'w': width.toString(),
    ...(height && { h: height.toString() })
  })

  return `${photo.urls.raw}&${params}`
}

// Predefined photo categories for the agency
export const PHOTO_CATEGORIES = {
  architecture: 'modern architecture interior design',
  business: 'business meeting professional office',
  creative: 'creative workspace design studio',
  technology: 'technology computer workspace',
  photography: 'photography camera studio',
  team: 'team collaboration meeting'
} as const

export type PhotoCategory = keyof typeof PHOTO_CATEGORIES